<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文化套餐预订功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #1890ff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 文化套餐预订功能测试</h1>
        
        <form id="bookingForm">
            <div class="form-group">
                <label for="packageId">套餐ID:</label>
                <input type="number" id="packageId" value="12" required>
            </div>
            
            <div class="form-group">
                <label for="bookingDate">预订日期:</label>
                <input type="date" id="bookingDate" required>
            </div>
            
            <div class="form-group">
                <label for="participantCount">参与人数:</label>
                <input type="number" id="participantCount" value="2" min="1" max="20" required>
            </div>
            
            <div class="form-group">
                <label for="contactName">联系人姓名:</label>
                <input type="text" id="contactName" value="前端测试用户" required>
            </div>
            
            <div class="form-group">
                <label for="contactPhone">联系电话:</label>
                <input type="tel" id="contactPhone" value="13900139000" required>
            </div>
            
            <div class="form-group">
                <label for="contactEmail">联系邮箱:</label>
                <input type="email" id="contactEmail" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="specialRequirements">特殊要求:</label>
                <textarea id="specialRequirements" rows="3" placeholder="请输入特殊要求...">前端功能测试</textarea>
            </div>
            
            <div class="form-group">
                <label for="participantInfo">参与者信息:</label>
                <textarea id="participantInfo" rows="3" placeholder="请输入参与者详细信息...">2人前端测试</textarea>
            </div>
            
            <button type="submit" id="submitBtn">提交预订</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 设置默认日期为明天
        document.getElementById('bookingDate').valueAsDate = new Date(Date.now() + 24*60*60*1000);
        
        // 模拟前端预订服务
        class TestPackageBookingService {
            async createBooking(packageId, bookingData) {
                try {
                    console.log('创建文化套餐预订:', { packageId, bookingData });
                    
                    const response = await fetch(`http://localhost:8080/api/packages/${packageId}/book`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            bookingDate: bookingData.bookingDate,
                            participantCount: bookingData.participantCount,
                            contactName: bookingData.contactName,
                            contactPhone: bookingData.contactPhone,
                            contactEmail: bookingData.contactEmail,
                            specialRequirements: bookingData.specialRequirements,
                            participantInfo: bookingData.participantInfo
                        })
                    });
                    
                    const result = await response.json();
                    console.log('预订创建响应:', result);
                    return result;
                } catch (error) {
                    console.error('创建预订失败:', error);
                    throw error;
                }
            }
        }
        
        const bookingService = new TestPackageBookingService();
        
        document.getElementById('bookingForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';
            
            // 清除之前的结果
            resultDiv.innerHTML = '';
            
            try {
                // 收集表单数据
                const formData = new FormData(e.target);
                const packageId = parseInt(document.getElementById('packageId').value);
                const bookingData = {
                    bookingDate: document.getElementById('bookingDate').value,
                    participantCount: parseInt(document.getElementById('participantCount').value),
                    contactName: document.getElementById('contactName').value,
                    contactPhone: document.getElementById('contactPhone').value,
                    contactEmail: document.getElementById('contactEmail').value,
                    specialRequirements: document.getElementById('specialRequirements').value,
                    participantInfo: document.getElementById('participantInfo').value
                };
                
                console.log('提交预订数据:', bookingData);
                
                // 调用预订API
                const response = await bookingService.createBooking(packageId, bookingData);
                
                if (response.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 预订成功！\n\n预订号：${response.data.bookingNumber}\n套餐名称：${response.data.packageName}\n预订日期：${response.data.bookingDate}\n参与人数：${response.data.participantCount}人\n联系人：${response.data.contactName}\n联系电话：${response.data.contactPhone}\n预订状态：${response.data.status}\n支付状态：${response.data.paymentStatus}\n\n完整响应：\n${JSON.stringify(response, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 预订失败：${response.message || '未知错误'}\n\n完整响应：\n${JSON.stringify(response, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误：${error.message}\n\n错误详情：\n${error.stack}`;
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = '提交预订';
            }
        });
    </script>
</body>
</html>
