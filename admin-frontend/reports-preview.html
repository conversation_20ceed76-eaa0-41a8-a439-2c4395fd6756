
<!DOCTYPE html>
<html>
<head>
    <title>报表页面预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .chart-container { height: 300px; display: flex; align-items: end; justify-content: space-around; padding: 20px 10px; }
        .bar { background: #1890ff; width: 30px; margin-bottom: 5px; border-radius: 2px; min-height: 2px; }
        .revenue-bar { background: #52c41a; width: 80px; border-radius: 4px; }
        .label { font-size: 10px; text-align: center; transform: rotate(-45deg); margin-top: 10px; }
        .value { font-size: 12px; font-weight: bold; color: #1890ff; margin-top: 15px; }
        .pie-chart { width: 150px; height: 150px; border-radius: 50%; display: flex; align-items: center; justify-content: center; }
        .legend { display: flex; align-items: center; margin: 10px 0; }
        .legend-color { width: 16px; height: 16px; margin-right: 8px; border-radius: 2px; }
    </style>
</head>
<body>
    <h1>📊 报表页面图表预览</h1>
    
    <div class="card">
        <h3>📈 预订趋势（最近7天）</h3>
        <div class="chart-container">
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">08-31</div>
                <div class="value">0</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">09-01</div>
                <div class="value">0</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">09-02</div>
                <div class="value">0</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">09-03</div>
                <div class="value">0</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">09-04</div>
                <div class="value">0</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 200px;"></div>
                <div class="label">09-05</div>
                <div class="value">4</div>
            </div>
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: 0px;"></div>
                <div class="label">09-06</div>
                <div class="value">0</div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3>📊 酒店收入分布</h3>
        <div style="height: 300px; display: flex; align-items: end; justify-content: center; gap: 20px; padding: 20px;">
            <div style="display: flex; flex-direction: column; align-items: center;">
                <div class="revenue-bar" style="height: 200px;"></div>
                <div style="font-size: 12px; text-align: center; max-width: 100px; word-wrap: break-word;">稻城亚丁香格里拉大酒店</div>
                <div style="font-size: 14px; font-weight: bold; color: #52c41a; margin-top: 5px;">¥3344</div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h3>🥧 收入占比分析</h3>
        <div style="height: 300px; display: flex; align-items: center; justify-content: center;">
            <div style="display: flex; align-items: center; gap: 40px;">
                <div class="pie-chart" style="background: conic-gradient(#1890ff 0% 100%, #52c41a 100% 100%);">
                    <div style="width: 80px; height: 80px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px;">收入占比</div>
                </div>
                <div>
                    <div class="legend">
                        <div class="legend-color" style="background: #1890ff;"></div>
                        <div>稻城亚丁香格里拉大酒店: 100%</div>
                    </div></div>
            </div>
        </div>
    </div>
</body>
</html>