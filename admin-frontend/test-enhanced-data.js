import axios from 'axios';

async function testEnhancedDashboardData() {
  console.log('🎯 测试增强的仪表板数据...\n');

  try {
    // 1. 登录获取token
    console.log('1️⃣ 登录获取认证token...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 测试增强的仪表板数据API
    console.log('\n2️⃣ 测试增强的仪表板数据API...');
    const dashboardResponse = await axios.get('http://localhost:8080/api/api/dashboard/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ API响应状态:', dashboardResponse.status);
    
    const dashboardData = dashboardResponse.data;
    console.log('📊 响应数据结构:');
    console.log('  - success:', dashboardData.success);
    console.log('  - message:', dashboardData.message);
    console.log('  - data存在:', !!dashboardData.data);

    if (dashboardData.data) {
      const data = dashboardData.data;
      console.log('\n📈 增强数据验证:');
      
      // 验证预订趋势数据
      if (data.bookingTrend && data.bookingTrend.length > 0) {
        console.log('✅ 预订趋势数据:', data.bookingTrend.length, '个数据点');
        console.log('📊 预订趋势详情:');
        
        let totalBookings = 0;
        let totalRevenue = 0;
        
        data.bookingTrend.forEach((item, index) => {
          totalBookings += item.bookingCount;
          totalRevenue += parseFloat(item.revenue || 0);
          console.log(`   ${item.date}: ${item.bookingCount}预订, ¥${item.revenue}收入`);
        });
        
        console.log(`📊 7天总计: ${totalBookings}预订, ¥${totalRevenue}收入`);
        console.log(`📊 日均预订: ${(totalBookings/7).toFixed(1)}个`);
        console.log(`📊 日均收入: ¥${(totalRevenue/7).toFixed(0)}`);
      } else {
        console.log('❌ 预订趋势数据为空');
      }

      // 验证收入分布数据
      if (data.revenueDistribution && data.revenueDistribution.length > 0) {
        console.log('\n✅ 收入分布数据:', data.revenueDistribution.length, '个酒店');
        console.log('🏨 酒店收入分布详情:');
        
        let totalAmount = 0;
        let totalPercentage = 0;
        
        data.revenueDistribution.forEach((item, index) => {
          totalAmount += parseFloat(item.amount);
          totalPercentage += item.percentage;
          console.log(`   ${index + 1}. ${item.category}: ¥${item.amount} (${item.percentage}%)`);
        });
        
        console.log(`💰 总收入: ¥${totalAmount}`);
        console.log(`📊 百分比总和: ${totalPercentage}%`);
        
        if (Math.abs(totalPercentage - 100) < 0.1) {
          console.log('✅ 百分比数据一致性验证通过');
        } else {
          console.log('❌ 百分比数据一致性验证失败');
        }
      } else {
        console.log('❌ 收入分布数据为空');
      }

      // 验证关键指标
      if (data.metrics && data.metrics.length > 0) {
        console.log('\n✅ 关键指标数据:', data.metrics.length, '个指标');
        console.log('📊 关键指标详情:');
        data.metrics.forEach((metric, index) => {
          console.log(`   ${index + 1}. ${metric.title}: ${metric.value} (${metric.trend} ${metric.trendType})`);
        });
      } else {
        console.log('❌ 关键指标数据为空');
      }

      // 验证入住率
      console.log('\n🏨 入住率:', data.occupancyRate || 0, '%');
      
      if (data.occupancyRate >= 50 && data.occupancyRate <= 100) {
        console.log('✅ 入住率数据合理');
      } else {
        console.log('⚠️ 入住率数据可能需要调整');
      }
    }

    // 3. 数据质量评估
    console.log('\n3️⃣ 数据质量评估...');
    const data = dashboardData.data;
    
    let qualityScore = 0;
    let maxScore = 0;
    
    // 预订趋势质量评估
    maxScore += 25;
    if (data.bookingTrend && data.bookingTrend.length === 7) {
      const totalBookings = data.bookingTrend.reduce((sum, item) => sum + item.bookingCount, 0);
      if (totalBookings >= 50) {
        qualityScore += 25;
        console.log('✅ 预订趋势数据质量: 优秀 (25/25分)');
      } else if (totalBookings >= 20) {
        qualityScore += 15;
        console.log('✅ 预订趋势数据质量: 良好 (15/25分)');
      } else {
        qualityScore += 5;
        console.log('⚠️ 预订趋势数据质量: 一般 (5/25分)');
      }
    }
    
    // 收入分布质量评估
    maxScore += 25;
    if (data.revenueDistribution && data.revenueDistribution.length >= 3) {
      const totalPercentage = data.revenueDistribution.reduce((sum, item) => sum + item.percentage, 0);
      if (Math.abs(totalPercentage - 100) < 0.1 && data.revenueDistribution.length >= 5) {
        qualityScore += 25;
        console.log('✅ 收入分布数据质量: 优秀 (25/25分)');
      } else if (Math.abs(totalPercentage - 100) < 1) {
        qualityScore += 15;
        console.log('✅ 收入分布数据质量: 良好 (15/25分)');
      } else {
        qualityScore += 5;
        console.log('⚠️ 收入分布数据质量: 一般 (5/25分)');
      }
    }
    
    // 关键指标质量评估
    maxScore += 25;
    if (data.metrics && data.metrics.length === 4) {
      qualityScore += 25;
      console.log('✅ 关键指标数据质量: 优秀 (25/25分)');
    }
    
    // 入住率质量评估
    maxScore += 25;
    if (data.occupancyRate >= 50 && data.occupancyRate <= 100) {
      qualityScore += 25;
      console.log('✅ 入住率数据质量: 优秀 (25/25分)');
    }
    
    const qualityPercentage = (qualityScore / maxScore * 100).toFixed(1);
    console.log(`\n📊 总体数据质量评分: ${qualityScore}/${maxScore} (${qualityPercentage}%)`);
    
    if (qualityPercentage >= 90) {
      console.log('🎉 数据质量评级: 优秀 - 适合生产环境演示');
    } else if (qualityPercentage >= 70) {
      console.log('👍 数据质量评级: 良好 - 基本满足演示需求');
    } else {
      console.log('⚠️ 数据质量评级: 需要改进');
    }

    // 4. 生成数据报告
    console.log('\n4️⃣ 生成数据增强报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      dataQuality: {
        score: qualityScore,
        maxScore: maxScore,
        percentage: qualityPercentage
      },
      bookingTrend: {
        dataPoints: data.bookingTrend?.length || 0,
        totalBookings: data.bookingTrend?.reduce((sum, item) => sum + item.bookingCount, 0) || 0,
        totalRevenue: data.bookingTrend?.reduce((sum, item) => sum + parseFloat(item.revenue || 0), 0) || 0
      },
      revenueDistribution: {
        hotels: data.revenueDistribution?.length || 0,
        totalAmount: data.revenueDistribution?.reduce((sum, item) => sum + parseFloat(item.amount), 0) || 0,
        percentageSum: data.revenueDistribution?.reduce((sum, item) => sum + item.percentage, 0) || 0
      },
      metrics: {
        count: data.metrics?.length || 0,
        items: data.metrics || []
      },
      occupancyRate: data.occupancyRate || 0
    };
    
    console.log('📋 数据增强报告已生成');
    console.log('📊 报告摘要:');
    console.log(`   - 预订趋势: ${report.bookingTrend.dataPoints}天数据, ${report.bookingTrend.totalBookings}预订`);
    console.log(`   - 收入分布: ${report.revenueDistribution.hotels}个酒店, ¥${report.revenueDistribution.totalAmount}`);
    console.log(`   - 关键指标: ${report.metrics.count}个指标`);
    console.log(`   - 入住率: ${report.occupancyRate}%`);

    console.log('\n🎉 增强数据测试完成！');
    console.log('\n🌐 访问地址:');
    console.log('   报表页面: http://localhost:3004/admin/reports');
    console.log('   管理后台: http://localhost:3004/admin');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testEnhancedDashboardData();
