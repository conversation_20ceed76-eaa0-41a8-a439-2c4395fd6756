<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-data {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>📊 图表显示测试页面</h1>
    
    <div class="chart-container">
        <div class="chart-title">🔍 测试数据结构</div>
        <div class="test-data" id="testData">
            正在加载测试数据...
        </div>
    </div>

    <div class="chart-container">
        <div class="chart-title">📈 预订趋势测试</div>
        <div id="lineChart" style="height: 300px;"></div>
    </div>

    <div class="chart-container">
        <div class="chart-title">📊 收入分布测试</div>
        <div id="columnChart" style="height: 300px;"></div>
    </div>

    <div class="chart-container">
        <div class="chart-title">🥧 收入占比测试</div>
        <div id="pieChart" style="height: 300px;"></div>
    </div>

    <script>
        // 模拟测试数据
        const testData = {
            bookingTrend: [
                { date: '2025-08-31', bookingCount: 0, revenue: 0 },
                { date: '2025-09-01', bookingCount: 2, revenue: 1200 },
                { date: '2025-09-02', bookingCount: 3, revenue: 1800 },
                { date: '2025-09-03', bookingCount: 1, revenue: 600 },
                { date: '2025-09-04', bookingCount: 4, revenue: 2400 },
                { date: '2025-09-05', bookingCount: 4, revenue: 3344 },
                { date: '2025-09-06', bookingCount: 2, revenue: 1200 }
            ],
            revenueDistribution: [
                { category: '稻城亚丁香格里拉大酒店', amount: 3344.00, percentage: 60.0 },
                { category: '康定情歌大酒店', amount: 2000.00, percentage: 40.0 }
            ],
            metrics: [
                { title: '总预订数', value: '16', trend: '+12%', trendType: 'up' },
                { title: '总用户数', value: '13', trend: '+8%', trendType: 'up' },
                { title: '总酒店数', value: '10', trend: '+2%', trendType: 'up' },
                { title: '本月收入', value: '¥5344.00', trend: '+15%', trendType: 'up' }
            ],
            occupancyRate: 65.5
        };

        // 显示测试数据
        document.getElementById('testData').innerHTML = JSON.stringify(testData, null, 2);

        // 创建简单的HTML图表（不依赖外部库）
        function createSimpleLineChart() {
            const container = document.getElementById('lineChart');
            const data = testData.bookingTrend;
            const maxValue = Math.max(...data.map(d => d.bookingCount));
            
            let html = '<div style="display: flex; align-items: end; height: 250px; gap: 10px;">';
            data.forEach((item, index) => {
                const height = (item.bookingCount / maxValue) * 200;
                html += `
                    <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                        <div style="background: #1890ff; width: 30px; height: ${height}px; margin-bottom: 5px; border-radius: 2px;"></div>
                        <div style="font-size: 10px; text-align: center; transform: rotate(-45deg); margin-top: 10px;">
                            ${item.date.slice(5)}
                        </div>
                        <div style="font-size: 12px; font-weight: bold; color: #1890ff; margin-top: 15px;">
                            ${item.bookingCount}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;
        }

        function createSimpleColumnChart() {
            const container = document.getElementById('columnChart');
            const data = testData.revenueDistribution;
            const maxValue = Math.max(...data.map(d => d.amount));
            
            let html = '<div style="display: flex; align-items: end; height: 250px; gap: 20px; justify-content: center;">';
            data.forEach((item, index) => {
                const height = (item.amount / maxValue) * 200;
                html += `
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="background: #52c41a; width: 80px; height: ${height}px; margin-bottom: 5px; border-radius: 4px;"></div>
                        <div style="font-size: 12px; text-align: center; max-width: 100px; word-wrap: break-word;">
                            ${item.category}
                        </div>
                        <div style="font-size: 14px; font-weight: bold; color: #52c41a; margin-top: 5px;">
                            ¥${item.amount}
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;
        }

        function createSimplePieChart() {
            const container = document.getElementById('pieChart');
            const data = testData.revenueDistribution;
            
            let html = '<div style="display: flex; align-items: center; justify-content: center; height: 250px;">';
            html += '<div style="display: flex; align-items: center; gap: 40px;">';
            
            // 简单的饼图表示
            html += '<div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(#1890ff 0% 60%, #52c41a 60% 100%); display: flex; align-items: center; justify-content: center;">';
            html += '<div style="width: 80px; height: 80px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold;">收入占比</div>';
            html += '</div>';
            
            // 图例
            html += '<div>';
            data.forEach((item, index) => {
                const color = index === 0 ? '#1890ff' : '#52c41a';
                html += `
                    <div style="display: flex; align-items: center; margin: 10px 0;">
                        <div style="width: 16px; height: 16px; background: ${color}; margin-right: 8px; border-radius: 2px;"></div>
                        <div style="font-size: 14px;">
                            ${item.category}: ${item.percentage}%
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            html += '</div></div>';
            container.innerHTML = html;
        }

        // 创建图表
        createSimpleLineChart();
        createSimpleColumnChart();
        createSimplePieChart();

        console.log('📊 图表测试页面已加载');
        console.log('测试数据:', testData);
    </script>
</body>
</html>
