import axios from 'axios';

async function testReportsPageAPI() {
  console.log('🔍 测试报表页面API功能...\n');

  try {
    // 1. 首先登录获取token
    console.log('1️⃣ 登录获取认证token...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功，获取到token');

    // 2. 测试仪表板数据API
    console.log('\n2️⃣ 测试仪表板数据API...');
    const dashboardResponse = await axios.get('http://localhost:8080/api/api/dashboard/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 仪表板API响应状态:', dashboardResponse.status);
    
    const dashboardData = dashboardResponse.data;
    console.log('📊 响应数据结构检查:');
    console.log('  - success:', dashboardData.success);
    console.log('  - message:', dashboardData.message);
    console.log('  - data存在:', !!dashboardData.data);

    if (dashboardData.data) {
      const data = dashboardData.data;
      console.log('\n📈 仪表板数据详情:');
      console.log('  - 预订趋势数据点:', data.bookingTrend?.length || 0);
      console.log('  - 收入分布数据点:', data.revenueDistribution?.length || 0);
      console.log('  - 关键指标数量:', data.metrics?.length || 0);
      console.log('  - 入住率:', data.occupancyRate || 0, '%');

      // 检查预订趋势数据
      if (data.bookingTrend && data.bookingTrend.length > 0) {
        console.log('\n📊 预订趋势样本数据:');
        data.bookingTrend.slice(0, 3).forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.date}: ${item.bookingCount}预订, ¥${item.revenue}收入`);
        });
      }

      // 检查收入分布数据
      if (data.revenueDistribution && data.revenueDistribution.length > 0) {
        console.log('\n💰 收入分布数据:');
        data.revenueDistribution.forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.category}: ¥${item.amount} (${item.percentage}%)`);
        });
      }

      // 检查关键指标
      if (data.metrics && data.metrics.length > 0) {
        console.log('\n📊 关键指标:');
        data.metrics.forEach((metric, index) => {
          console.log(`    ${index + 1}. ${metric.title}: ${metric.value} (${metric.trend} ${metric.trendType})`);
        });
      }
    }

    // 3. 测试前端API服务
    console.log('\n3️⃣ 测试前端API服务...');
    
    // 模拟前端API调用
    const frontendApiTest = await axios.get('http://localhost:3004/api/api/dashboard/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 前端代理API响应状态:', frontendApiTest.status);
    console.log('📊 前端API数据一致性:', JSON.stringify(frontendApiTest.data) === JSON.stringify(dashboardResponse.data));

    console.log('\n🎉 报表页面API测试完成！');
    console.log('\n📋 测试结果总结:');
    console.log('✅ 后端API正常工作');
    console.log('✅ 数据结构完整');
    console.log('✅ 前端代理正常');
    console.log('✅ 认证机制正常');
    
    console.log('\n🌐 访问报表页面:');
    console.log('   主页: http://localhost:3004');
    console.log('   报表页面: http://localhost:3004/reports');
    console.log('   (注意：不是 http://localhost:3001)');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确认后端服务运行在 http://localhost:8080');
    console.log('2. 确认前端服务运行在 http://localhost:3004');
    console.log('3. 检查数据库连接是否正常');
    console.log('4. 清除浏览器缓存');
  }
}

// 运行测试
testReportsPageAPI();
