// 调试预订管理系统统计问题的脚本
// 运行方式: node debug-statistics.js

console.log('=== 预订管理系统统计问题调试 ===\n');

// 模拟完整的预订数据（从实际系统复制）
const mockBookings = [
  {
    id: 1,
    bookingNumber: 'PB202509061001',
    userId: 1,
    userName: '张三',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-10',
    bookingTime: '09:00',
    participantCount: 2,
    unitPrice: 399,
    totalAmount: 798,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '张三',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望安排中文导游',
    participantInfo: '张三（成人）、李四（成人）',
    createdAt: '2025-09-06T10:30:00',
    updatedAt: '2025-09-06T10:30:00',
  },
  {
    id: 2,
    bookingNumber: 'PB202509061002',
    userId: 2,
    userName: '李四',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-12',
    bookingTime: '14:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '李四',
    contactPhone: '13800138002',
    contactEmail: '<EMAIL>',
    specialRequirements: '对海鲜过敏',
    participantInfo: '李四（成人）',
    confirmedAt: '2025-09-06T11:00:00',
    createdAt: '2025-09-06T09:15:00',
    updatedAt: '2025-09-06T11:00:00',
  },
  {
    id: 3,
    bookingNumber: 'PB202509061003',
    userId: 3,
    userName: '王五',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-15',
    bookingTime: '06:00',
    participantCount: 4,
    unitPrice: 899,
    totalAmount: 3596,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '王五',
    contactPhone: '13800138003',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要专业摄影指导',
    participantInfo: '王五（成人）、赵六（成人）、孙七（成人）、周八（成人）',
    createdAt: '2025-09-06T08:45:00',
    updatedAt: '2025-09-06T08:45:00',
  },
  {
    id: 4,
    bookingNumber: 'PB202509061004',
    userId: 4,
    userName: '赵六',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-08',
    bookingTime: '09:00',
    participantCount: 1,
    unitPrice: 399,
    totalAmount: 399,
    status: 'COMPLETED',
    paymentStatus: 'PAID',
    contactName: '赵六',
    contactPhone: '13800138004',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '赵六（成人）',
    confirmedAt: '2025-09-05T14:00:00',
    createdAt: '2025-09-05T10:20:00',
    updatedAt: '2025-09-08T18:00:00',
  },
  {
    id: 5,
    bookingNumber: 'PB202509061005',
    userId: 5,
    userName: '孙七',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-07',
    bookingTime: '14:00',
    participantCount: 2,
    unitPrice: 299,
    totalAmount: 598,
    status: 'CANCELLED',
    paymentStatus: 'REFUNDED',
    contactName: '孙七',
    contactPhone: '13800138005',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '孙七（成人）、周八（成人）',
    cancelledAt: '2025-09-06T12:00:00',
    cancellationReason: '行程变更，无法参加',
    createdAt: '2025-09-05T16:30:00',
    updatedAt: '2025-09-06T12:00:00',
  },
  {
    id: 6,
    bookingNumber: 'PB202509061006',
    userId: 6,
    userName: '周八',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '06:00',
    participantCount: 2,
    unitPrice: 899,
    totalAmount: 1798,
    status: 'IN_PROGRESS',
    paymentStatus: 'PAID',
    contactName: '周八',
    contactPhone: '13800138006',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要高原反应药物',
    participantInfo: '周八（成人）、钱九（成人）',
    confirmedAt: '2025-09-06T08:00:00',
    createdAt: '2025-09-06T07:30:00',
    updatedAt: '2025-09-06T08:00:00',
  },
  {
    id: 7,
    bookingNumber: 'PB202509061007',
    userId: 7,
    userName: '钱九',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '14:00',
    participantCount: 3,
    unitPrice: 399,
    totalAmount: 1197,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '钱九',
    contactPhone: '13800138007',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望有儿童适合的活动',
    participantInfo: '钱九（成人）、孙十（成人）、小明（儿童）',
    createdAt: '2025-09-06T13:15:00',
    updatedAt: '2025-09-06T13:15:00',
  },
  {
    id: 8,
    bookingNumber: 'PB202509061008',
    userId: 8,
    userName: '孙十',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '16:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PARTIAL',
    contactName: '孙十',
    contactPhone: '13800138008',
    contactEmail: '<EMAIL>',
    specialRequirements: '素食主义者',
    participantInfo: '孙十（成人）',
    confirmedAt: '2025-09-06T14:30:00',
    createdAt: '2025-09-06T14:00:00',
    updatedAt: '2025-09-06T14:30:00',
  },
  {
    id: 9,
    bookingNumber: 'PB202509061009',
    userId: 9,
    userName: '李十一',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-20',
    bookingTime: '06:00',
    participantCount: 1,
    unitPrice: 899,
    totalAmount: 899,
    status: 'NO_SHOW',
    paymentStatus: 'PAID',
    contactName: '李十一',
    contactPhone: '13800138009',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '李十一（成人）',
    confirmedAt: '2025-09-05T10:00:00',
    createdAt: '2025-09-05T09:30:00',
    updatedAt: '2025-09-20T06:30:00',
  },
  {
    id: 10,
    bookingNumber: 'PB202509061010',
    userId: 10,
    userName: '王十二',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-25',
    bookingTime: '09:00',
    participantCount: 4,
    unitPrice: 399,
    totalAmount: 1596,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '王十二',
    contactPhone: '13800138010',
    contactEmail: '<EMAIL>',
    specialRequirements: '团体预订，需要团体优惠',
    participantInfo: '王十二（成人）、张十三（成人）、李十四（成人）、赵十五（成人）',
    confirmedAt: '2025-09-06T15:00:00',
    createdAt: '2025-09-06T14:45:00',
    updatedAt: '2025-09-06T15:00:00',
  },
];

console.log('📊 原始数据分析:');
console.log(`总预订数: ${mockBookings.length}`);
console.log('');

// 问题1: 今日预订统计问题
console.log('🔍 问题1: 今日预订统计');
const currentDate = new Date().toISOString().split('T')[0];
const testDate = '2025-09-06';
console.log(`当前系统日期: ${currentDate}`);
console.log(`测试数据中的"今日"日期: ${testDate}`);

const todayBookingsWithCurrentDate = mockBookings.filter(b => b.bookingDate === currentDate);
const todayBookingsWithTestDate = mockBookings.filter(b => b.bookingDate === testDate);

console.log(`使用当前系统日期的今日预订数: ${todayBookingsWithCurrentDate.length}`);
console.log(`使用测试日期的今日预订数: ${todayBookingsWithTestDate.length}`);
console.log(`❌ 问题确认: 日期不匹配导致今日预订统计错误\n`);

// 问题2: 收入计算验证
console.log('🔍 问题2: 收入计算验证');
let totalRevenue = 0;
let paidRevenue = 0;
let pendingRevenue = 0;
let refundedRevenue = 0;
let partialRevenue = 0;

const paymentBreakdown = {
  PENDING: { count: 0, amount: 0 },
  PARTIAL: { count: 0, amount: 0 },
  PAID: { count: 0, amount: 0 },
  REFUNDED: { count: 0, amount: 0 },
  FAILED: { count: 0, amount: 0 },
};

mockBookings.forEach(booking => {
  totalRevenue += booking.totalAmount;
  paymentBreakdown[booking.paymentStatus].count++;
  paymentBreakdown[booking.paymentStatus].amount += booking.totalAmount;
  
  if (booking.paymentStatus === 'PAID') {
    paidRevenue += booking.totalAmount;
  } else if (booking.paymentStatus === 'PENDING') {
    pendingRevenue += booking.totalAmount;
  } else if (booking.paymentStatus === 'REFUNDED') {
    refundedRevenue += booking.totalAmount;
  } else if (booking.paymentStatus === 'PARTIAL') {
    // 当前逻辑：假设已支付一半
    paidRevenue += booking.totalAmount * 0.5;
    pendingRevenue += booking.totalAmount * 0.5;
    partialRevenue += booking.totalAmount;
  }
});

console.log('支付状态详细分析:');
Object.entries(paymentBreakdown).forEach(([status, data]) => {
  console.log(`${status}: ${data.count}个预订, 总金额¥${data.amount.toFixed(2)}`);
});

console.log('\n收入计算结果:');
console.log(`总预订金额: ¥${totalRevenue.toFixed(2)}`);
console.log(`已收入金额: ¥${paidRevenue.toFixed(2)}`);
console.log(`待收金额: ¥${pendingRevenue.toFixed(2)}`);
console.log(`已退款金额: ¥${refundedRevenue.toFixed(2)}`);
console.log(`部分支付金额: ¥${partialRevenue.toFixed(2)}`);

const calculatedTotal = paidRevenue + pendingRevenue + refundedRevenue;
console.log(`\n验证: 各状态金额总和: ¥${calculatedTotal.toFixed(2)}`);
console.log(`验证: 与总预订金额差异: ¥${Math.abs(calculatedTotal - totalRevenue).toFixed(2)}`);

if (Math.abs(calculatedTotal - totalRevenue) > 0.01) {
  console.log('❌ 问题确认: 收入计算存在不一致');
} else {
  console.log('✅ 收入计算一致性验证通过');
}

// 问题3: 状态统计验证
console.log('\n🔍 问题3: 状态统计验证');
const statusBreakdown = {
  PENDING: { count: 0, amount: 0 },
  CONFIRMED: { count: 0, amount: 0 },
  IN_PROGRESS: { count: 0, amount: 0 },
  COMPLETED: { count: 0, amount: 0 },
  CANCELLED: { count: 0, amount: 0 },
  NO_SHOW: { count: 0, amount: 0 },
};

mockBookings.forEach(booking => {
  statusBreakdown[booking.status].count++;
  statusBreakdown[booking.status].amount += booking.totalAmount;
});

console.log('预订状态详细分析:');
Object.entries(statusBreakdown).forEach(([status, data]) => {
  console.log(`${status}: ${data.count}个预订, 总金额¥${data.amount.toFixed(2)}`);
});

const pendingBookings = statusBreakdown.PENDING.count;
console.log(`\n待确认预订数: ${pendingBookings}`);

// 问题4: 收入占比计算验证
console.log('\n🔍 问题4: 收入占比计算验证');
if (totalRevenue > 0) {
  const revenueRatio = (paidRevenue / totalRevenue) * 100;
  const pendingRatio = (pendingRevenue / totalRevenue) * 100;
  const refundedRatio = (refundedRevenue / totalRevenue) * 100;
  
  console.log(`收入占比: ${revenueRatio.toFixed(1)}%`);
  console.log(`待收占比: ${pendingRatio.toFixed(1)}%`);
  console.log(`退款占比: ${refundedRatio.toFixed(1)}%`);
  
  const totalRatio = revenueRatio + pendingRatio + refundedRatio;
  console.log(`总占比: ${totalRatio.toFixed(1)}%`);
  
  if (Math.abs(totalRatio - 100) > 0.1) {
    console.log('❌ 问题确认: 占比计算不等于100%');
  } else {
    console.log('✅ 占比计算验证通过');
  }
}

console.log('\n=== 调试总结 ===');
console.log('发现的问题:');
console.log('1. ❌ 今日预订统计: 系统日期与测试数据日期不匹配');
console.log('2. ⚠️  PARTIAL支付状态处理: 可能导致重复计算');
console.log('3. ⚠️  收入占比计算: 需要验证PARTIAL状态的处理逻辑');
console.log('4. ⚠️  数据同步: 状态变更时统计更新的一致性');
