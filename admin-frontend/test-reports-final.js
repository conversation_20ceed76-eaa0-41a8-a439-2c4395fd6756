import axios from 'axios';

async function testReportsPageFinal() {
  console.log('🎯 最终报表页面测试...\n');

  try {
    // 1. 登录获取token
    console.log('1️⃣ 登录获取认证token...');
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    if (!loginResponse.data.success) {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 测试仪表板数据API
    console.log('\n2️⃣ 测试仪表板数据API...');
    const dashboardResponse = await axios.get('http://localhost:8080/api/api/dashboard/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ API响应状态:', dashboardResponse.status);
    
    const dashboardData = dashboardResponse.data;
    console.log('📊 响应数据结构:');
    console.log('  - success:', dashboardData.success);
    console.log('  - message:', dashboardData.message);
    console.log('  - data存在:', !!dashboardData.data);

    if (dashboardData.data) {
      const data = dashboardData.data;
      console.log('\n📈 图表数据验证:');
      
      // 验证预订趋势数据
      if (data.bookingTrend && data.bookingTrend.length > 0) {
        console.log('✅ 预订趋势数据:', data.bookingTrend.length, '个数据点');
        const maxBookings = Math.max(...data.bookingTrend.map(d => d.bookingCount));
        console.log('   最大预订数:', maxBookings);
        
        // 模拟图表高度计算
        data.bookingTrend.forEach((item, index) => {
          const height = maxBookings > 0 ? (item.bookingCount / maxBookings) * 200 : 0;
          console.log(`   ${item.date}: ${item.bookingCount}预订 -> 柱高${height.toFixed(1)}px`);
        });
      } else {
        console.log('❌ 预订趋势数据为空');
      }

      // 验证收入分布数据
      if (data.revenueDistribution && data.revenueDistribution.length > 0) {
        console.log('✅ 收入分布数据:', data.revenueDistribution.length, '个数据点');
        const maxRevenue = Math.max(...data.revenueDistribution.map(d => d.amount));
        console.log('   最大收入:', maxRevenue);
        
        // 模拟图表高度计算
        data.revenueDistribution.forEach((item, index) => {
          const height = maxRevenue > 0 ? (item.amount / maxRevenue) * 200 : 0;
          console.log(`   ${item.category}: ¥${item.amount} (${item.percentage}%) -> 柱高${height.toFixed(1)}px`);
        });
      } else {
        console.log('❌ 收入分布数据为空');
      }

      // 验证关键指标
      if (data.metrics && data.metrics.length > 0) {
        console.log('✅ 关键指标数据:', data.metrics.length, '个指标');
        data.metrics.forEach((metric, index) => {
          console.log(`   ${index + 1}. ${metric.title}: ${metric.value} (${metric.trend})`);
        });
      } else {
        console.log('❌ 关键指标数据为空');
      }

      // 验证入住率
      console.log('✅ 入住率:', data.occupancyRate || 0, '%');
    }

    // 3. 测试前端API
    console.log('\n3️⃣ 测试前端API代理...');
    const frontendResponse = await axios.get('http://localhost:3004/api/api/dashboard/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 前端代理状态:', frontendResponse.status);
    console.log('✅ 数据一致性:', JSON.stringify(frontendResponse.data) === JSON.stringify(dashboardResponse.data));

    // 4. 生成图表预览HTML
    console.log('\n4️⃣ 生成图表预览...');
    const data = dashboardData.data;
    
    let htmlPreview = `
<!DOCTYPE html>
<html>
<head>
    <title>报表页面预览</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .chart-container { height: 300px; display: flex; align-items: end; justify-content: space-around; padding: 20px 10px; }
        .bar { background: #1890ff; width: 30px; margin-bottom: 5px; border-radius: 2px; min-height: 2px; }
        .revenue-bar { background: #52c41a; width: 80px; border-radius: 4px; }
        .label { font-size: 10px; text-align: center; transform: rotate(-45deg); margin-top: 10px; }
        .value { font-size: 12px; font-weight: bold; color: #1890ff; margin-top: 15px; }
        .pie-chart { width: 150px; height: 150px; border-radius: 50%; display: flex; align-items: center; justify-content: center; }
        .legend { display: flex; align-items: center; margin: 10px 0; }
        .legend-color { width: 16px; height: 16px; margin-right: 8px; border-radius: 2px; }
    </style>
</head>
<body>
    <h1>📊 报表页面图表预览</h1>
    
    <div class="card">
        <h3>📈 预订趋势（最近7天）</h3>
        <div class="chart-container">`;

    // 生成预订趋势图
    if (data.bookingTrend && data.bookingTrend.length > 0) {
      const maxBookings = Math.max(...data.bookingTrend.map(d => d.bookingCount));
      data.bookingTrend.forEach(item => {
        const height = maxBookings > 0 ? (item.bookingCount / maxBookings) * 200 : 2;
        htmlPreview += `
            <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                <div class="bar" style="height: ${height}px;"></div>
                <div class="label">${item.date.slice(5)}</div>
                <div class="value">${item.bookingCount}</div>
            </div>`;
      });
    }

    htmlPreview += `
        </div>
    </div>
    
    <div class="card">
        <h3>📊 酒店收入分布</h3>
        <div style="height: 300px; display: flex; align-items: end; justify-content: center; gap: 20px; padding: 20px;">`;

    // 生成收入分布图
    if (data.revenueDistribution && data.revenueDistribution.length > 0) {
      const maxRevenue = Math.max(...data.revenueDistribution.map(d => d.amount));
      data.revenueDistribution.forEach(item => {
        const height = maxRevenue > 0 ? (item.amount / maxRevenue) * 200 : 2;
        htmlPreview += `
            <div style="display: flex; flex-direction: column; align-items: center;">
                <div class="revenue-bar" style="height: ${height}px;"></div>
                <div style="font-size: 12px; text-align: center; max-width: 100px; word-wrap: break-word;">${item.category}</div>
                <div style="font-size: 14px; font-weight: bold; color: #52c41a; margin-top: 5px;">¥${item.amount}</div>
            </div>`;
      });
    }

    htmlPreview += `
        </div>
    </div>
    
    <div class="card">
        <h3>🥧 收入占比分析</h3>
        <div style="height: 300px; display: flex; align-items: center; justify-content: center;">
            <div style="display: flex; align-items: center; gap: 40px;">`;

    // 生成饼图
    if (data.revenueDistribution && data.revenueDistribution.length > 0) {
      const firstPercentage = data.revenueDistribution[0]?.percentage || 100;
      htmlPreview += `
                <div class="pie-chart" style="background: conic-gradient(#1890ff 0% ${firstPercentage}%, #52c41a ${firstPercentage}% 100%);">
                    <div style="width: 80px; height: 80px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 12px;">收入占比</div>
                </div>
                <div>`;
      
      data.revenueDistribution.forEach((item, index) => {
        const color = index === 0 ? '#1890ff' : '#52c41a';
        htmlPreview += `
                    <div class="legend">
                        <div class="legend-color" style="background: ${color};"></div>
                        <div>${item.category}: ${item.percentage}%</div>
                    </div>`;
      });
      
      htmlPreview += `</div>`;
    }

    htmlPreview += `
            </div>
        </div>
    </div>
</body>
</html>`;

    // 保存预览文件
    const fs = await import('fs');
    fs.writeFileSync('reports-preview.html', htmlPreview);
    console.log('✅ 图表预览已生成: reports-preview.html');

    console.log('\n🎉 报表页面测试完成！');
    console.log('\n📋 测试结果:');
    console.log('✅ API调用正常');
    console.log('✅ 数据结构完整');
    console.log('✅ 图表数据有效');
    console.log('✅ 前端代理正常');
    console.log('✅ 图表预览生成');
    
    console.log('\n🌐 访问地址:');
    console.log('   报表页面: http://localhost:3004/admin/reports');
    console.log('   图表预览: file://' + process.cwd() + '/reports-preview.html');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testReportsPageFinal();
