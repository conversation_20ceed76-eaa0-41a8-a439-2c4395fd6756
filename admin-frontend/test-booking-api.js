// 测试预订管理API功能的脚本
// 运行方式: node test-booking-api.js

// 模拟导入（在实际环境中需要适当的模块导入）
const mockBookings = [
  {
    id: 1,
    bookingNumber: 'PB202509061001',
    userId: 1,
    userName: '张三',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-10',
    bookingTime: '09:00',
    participantCount: 2,
    unitPrice: 399,
    totalAmount: 798,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '张三',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望安排中文导游',
    participantInfo: '张三（成人）、李四（成人）',
    createdAt: '2025-09-06T10:30:00',
    updatedAt: '2025-09-06T10:30:00',
  },
  {
    id: 2,
    bookingNumber: 'PB202509061002',
    userId: 2,
    userName: '李四',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-12',
    bookingTime: '14:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '李四',
    contactPhone: '13800138002',
    contactEmail: '<EMAIL>',
    specialRequirements: '对海鲜过敏',
    participantInfo: '李四（成人）',
    confirmedAt: '2025-09-06T11:00:00',
    createdAt: '2025-09-06T09:15:00',
    updatedAt: '2025-09-06T11:00:00',
  },
  {
    id: 3,
    bookingNumber: 'PB202509061003',
    userId: 3,
    userName: '王五',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-15',
    bookingTime: '06:00',
    participantCount: 4,
    unitPrice: 899,
    totalAmount: 3596,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '王五',
    contactPhone: '13800138003',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要专业摄影指导',
    participantInfo: '王五（成人）、赵六（成人）、孙七（成人）、周八（成人）',
    createdAt: '2025-09-06T08:45:00',
    updatedAt: '2025-09-06T08:45:00',
  },
  {
    id: 4,
    bookingNumber: 'PB202509061004',
    userId: 4,
    userName: '赵六',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-08',
    bookingTime: '09:00',
    participantCount: 1,
    unitPrice: 399,
    totalAmount: 399,
    status: 'COMPLETED',
    paymentStatus: 'PAID',
    contactName: '赵六',
    contactPhone: '13800138004',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '赵六（成人）',
    confirmedAt: '2025-09-05T14:00:00',
    createdAt: '2025-09-05T10:20:00',
    updatedAt: '2025-09-08T18:00:00',
  },
  {
    id: 5,
    bookingNumber: 'PB202509061005',
    userId: 5,
    userName: '孙七',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-07',
    bookingTime: '14:00',
    participantCount: 2,
    unitPrice: 299,
    totalAmount: 598,
    status: 'CANCELLED',
    paymentStatus: 'REFUNDED',
    contactName: '孙七',
    contactPhone: '13800138005',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '孙七（成人）、周八（成人）',
    cancelledAt: '2025-09-06T12:00:00',
    cancellationReason: '行程变更，无法参加',
    createdAt: '2025-09-05T16:30:00',
    updatedAt: '2025-09-06T12:00:00',
  },
  // 新增的测试数据
  {
    id: 6,
    bookingNumber: 'PB202509061006',
    userId: 6,
    userName: '周八',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '06:00',
    participantCount: 2,
    unitPrice: 899,
    totalAmount: 1798,
    status: 'IN_PROGRESS',
    paymentStatus: 'PAID',
    contactName: '周八',
    contactPhone: '13800138006',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要高原反应药物',
    participantInfo: '周八（成人）、钱九（成人）',
    confirmedAt: '2025-09-06T08:00:00',
    createdAt: '2025-09-06T07:30:00',
    updatedAt: '2025-09-06T08:00:00',
  },
  {
    id: 7,
    bookingNumber: 'PB202509061007',
    userId: 7,
    userName: '钱九',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '14:00',
    participantCount: 3,
    unitPrice: 399,
    totalAmount: 1197,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '钱九',
    contactPhone: '13800138007',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望有儿童适合的活动',
    participantInfo: '钱九（成人）、孙十（成人）、小明（儿童）',
    createdAt: '2025-09-06T13:15:00',
    updatedAt: '2025-09-06T13:15:00',
  },
  {
    id: 8,
    bookingNumber: 'PB202509061008',
    userId: 8,
    userName: '孙十',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '16:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PARTIAL',
    contactName: '孙十',
    contactPhone: '13800138008',
    contactEmail: '<EMAIL>',
    specialRequirements: '素食主义者',
    participantInfo: '孙十（成人）',
    confirmedAt: '2025-09-06T14:30:00',
    createdAt: '2025-09-06T14:00:00',
    updatedAt: '2025-09-06T14:30:00',
  },
  {
    id: 9,
    bookingNumber: 'PB202509061009',
    userId: 9,
    userName: '李十一',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-20',
    bookingTime: '06:00',
    participantCount: 1,
    unitPrice: 899,
    totalAmount: 899,
    status: 'NO_SHOW',
    paymentStatus: 'PAID',
    contactName: '李十一',
    contactPhone: '13800138009',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '李十一（成人）',
    confirmedAt: '2025-09-05T10:00:00',
    createdAt: '2025-09-05T09:30:00',
    updatedAt: '2025-09-20T06:30:00',
  },
  {
    id: 10,
    bookingNumber: 'PB202509061010',
    userId: 10,
    userName: '王十二',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-25',
    bookingTime: '09:00',
    participantCount: 4,
    unitPrice: 399,
    totalAmount: 1596,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '王十二',
    contactPhone: '13800138010',
    contactEmail: '<EMAIL>',
    specialRequirements: '团体预订，需要团体优惠',
    participantInfo: '王十二（成人）、张十三（成人）、李十四（成人）、赵十五（成人）',
    confirmedAt: '2025-09-06T15:00:00',
    createdAt: '2025-09-06T14:45:00',
    updatedAt: '2025-09-06T15:00:00',
  },
];

// 统计计算函数
function calculateStatistics() {
  const statusCounts = {
    PENDING: 0,
    CONFIRMED: 0,
    IN_PROGRESS: 0,
    COMPLETED: 0,
    CANCELLED: 0,
    NO_SHOW: 0,
  };

  const paymentStatusCounts = {
    PENDING: 0,
    PARTIAL: 0,
    PAID: 0,
    REFUNDED: 0,
    FAILED: 0,
  };

  const revenueByStatus = {
    PENDING: 0,
    CONFIRMED: 0,
    IN_PROGRESS: 0,
    COMPLETED: 0,
    CANCELLED: 0,
    NO_SHOW: 0,
  };

  let totalRevenue = 0;
  let paidRevenue = 0;
  let pendingRevenue = 0;
  let refundedRevenue = 0;
  let pendingBookings = 0;
  let todayBookings = 0;

  const today = '2025-09-06'; // 模拟今日日期

  mockBookings.forEach(booking => {
    // 统计预订状态
    statusCounts[booking.status]++;

    // 统计支付状态
    paymentStatusCounts[booking.paymentStatus]++;

    // 按状态统计收入
    revenueByStatus[booking.status] += booking.totalAmount;

    // 计算总收入（所有预订的金额）
    totalRevenue += booking.totalAmount;

    // 按支付状态计算收入
    if (booking.paymentStatus === 'PAID') {
      paidRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'PENDING') {
      pendingRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'REFUNDED') {
      refundedRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'PARTIAL') {
      // PARTIAL状态的预订，假设已支付一半
      paidRevenue += booking.totalAmount * 0.5;
      pendingRevenue += booking.totalAmount * 0.5;
    }

    // 统计待确认预订
    if (booking.status === 'PENDING') {
      pendingBookings++;
    }

    // 统计今日预订（按预订日期）
    if (booking.bookingDate === today) {
      todayBookings++;
    }
  });

  return {
    statusCounts,
    paymentStatusCounts,
    totalBookings: mockBookings.length,
    pendingBookings,
    todayBookings,
    monthlyRevenue: paidRevenue, // 本月收入使用已支付的金额
    totalRevenue,
    pendingRevenue,
    refundedRevenue,
    revenueByStatus,
  };
}

// 测试函数
function testBookingStatistics() {
  console.log('=== 预订管理系统统计测试 ===\n');
  
  const stats = calculateStatistics();
  
  console.log('📊 基础统计:');
  console.log(`总预订数: ${stats.totalBookings}`);
  console.log(`待确认预订: ${stats.pendingBookings}`);
  console.log(`今日新增: ${stats.todayBookings}`);
  console.log('');
  
  console.log('📈 预订状态统计:');
  Object.entries(stats.statusCounts).forEach(([status, count]) => {
    console.log(`${status}: ${count}`);
  });
  console.log('');
  
  console.log('💰 收入统计:');
  console.log(`总预订金额: ¥${stats.totalRevenue.toFixed(2)}`);
  console.log(`已收入金额: ¥${stats.monthlyRevenue.toFixed(2)}`);
  console.log(`待收金额: ¥${stats.pendingRevenue.toFixed(2)}`);
  console.log(`已退款金额: ¥${stats.refundedRevenue.toFixed(2)}`);
  console.log('');
  
  console.log('📊 收入占比:');
  const totalRevenue = stats.totalRevenue;
  if (totalRevenue > 0) {
    console.log(`收入占比: ${((stats.monthlyRevenue / totalRevenue) * 100).toFixed(1)}%`);
    console.log(`待收占比: ${((stats.pendingRevenue / totalRevenue) * 100).toFixed(1)}%`);
    console.log(`退款占比: ${((stats.refundedRevenue / totalRevenue) * 100).toFixed(1)}%`);
    console.log(`完成订单占比: ${((stats.revenueByStatus.COMPLETED / totalRevenue) * 100).toFixed(1)}%`);
  }
  console.log('');
  
  console.log('🎯 按状态收入分布:');
  Object.entries(stats.revenueByStatus).forEach(([status, revenue]) => {
    const percentage = totalRevenue > 0 ? ((revenue / totalRevenue) * 100).toFixed(1) : 0;
    console.log(`${status}: ¥${revenue.toFixed(2)} (${percentage}%)`);
  });
  
  console.log('\n=== 测试完成 ===');
  
  // 验证数据一致性
  console.log('\n🔍 数据一致性验证:');
  const totalByPayment = stats.monthlyRevenue + stats.pendingRevenue + stats.refundedRevenue;
  console.log(`支付状态总金额: ¥${totalByPayment.toFixed(2)}`);
  console.log(`预订总金额: ¥${stats.totalRevenue.toFixed(2)}`);
  console.log(`差额: ¥${Math.abs(totalByPayment - stats.totalRevenue).toFixed(2)}`);
  console.log(`数据一致性: ${Math.abs(totalByPayment - stats.totalRevenue) < 1 ? '✅ 通过' : '❌ 失败'}`);
}

// 运行测试
testBookingStatistics();
