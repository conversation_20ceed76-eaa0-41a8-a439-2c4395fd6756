import { request } from './api';

export interface PackageBooking {
  id: number;
  bookingNumber: string;
  userId?: number;
  userName?: string;
  packageId: number;
  packageName: string;
  packageLocation?: string;
  bookingDate: string;
  bookingTime?: string;
  participantCount: number;
  unitPrice: number;
  totalAmount: number;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  paymentStatus: 'PENDING' | 'PARTIAL' | 'PAID' | 'REFUNDED' | 'FAILED';
  contactName: string;
  contactPhone: string;
  contactEmail?: string;
  specialRequirements?: string;
  participantInfo?: string;
  confirmedAt?: string;
  cancelledAt?: string;
  cancellationReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BookingSearchParams {
  status?: string;
  paymentStatus?: string;
  startDate?: string;
  endDate?: string;
  keyword?: string;
  page?: number;
  size?: number;
}

export interface BookingStatistics {
  statusCounts: Record<string, number>;
  paymentStatusCounts: Record<string, number>;
  totalBookings: number;
  pendingBookings: number;
  todayBookings: number;
  monthlyRevenue: number;
  // 新增收入相关统计
  totalRevenue: number;
  pendingRevenue: number;
  refundedRevenue: number;
  revenueByStatus: Record<string, number>;
}

export interface ConfirmBookingRequest {
  remarks?: string;
}

export interface RejectBookingRequest {
  reason: string;
}

export interface CancelBookingRequest {
  reason?: string;
}

/**
 * 文化套餐预订管理API
 */
export const packageBookingApi = {
  /**
   * 获取所有预订列表
   */
  async getBookings(params: BookingSearchParams = {}) {
    const { page = 0, size = 20, ...searchParams } = params;
    return request.get('/admin/package-bookings', {
      params: {
        page,
        size,
        ...searchParams,
      },
    });
  },

  /**
   * 搜索预订
   */
  async searchBookings(params: BookingSearchParams) {
    const { page = 0, size = 20, ...searchParams } = params;
    return request.get('/admin/package-bookings/search', {
      params: {
        page,
        size,
        ...searchParams,
      },
    });
  },

  /**
   * 获取预订详情
   */
  async getBookingById(id: number) {
    return request.get(`/admin/package-bookings/${id}`);
  },

  /**
   * 根据预订号获取预订详情
   */
  async getBookingByNumber(bookingNumber: string) {
    return request.get(`/admin/package-bookings/number/${bookingNumber}`);
  },

  /**
   * 确认预订
   */
  async confirmBooking(id: number, data: ConfirmBookingRequest = {}) {
    return request.put(`/admin/package-bookings/${id}/confirm`, data);
  },

  /**
   * 拒绝预订
   */
  async rejectBooking(id: number, data: RejectBookingRequest) {
    return request.put(`/admin/package-bookings/${id}/reject`, data);
  },

  /**
   * 取消预订
   */
  async cancelBooking(id: number, data: CancelBookingRequest = {}) {
    return request.put(`/admin/package-bookings/${id}/cancel`, data);
  },

  /**
   * 完成预订
   */
  async completeBooking(id: number) {
    return request.put(`/admin/package-bookings/${id}/complete`);
  },

  /**
   * 获取预订统计信息
   */
  async getBookingStatistics() {
    return request.get('/admin/package-bookings/statistics');
  },
};

/**
 * 预订状态映射
 */
export const BOOKING_STATUS_MAP = {
  PENDING: { text: '待确认', color: 'orange' },
  CONFIRMED: { text: '已确认', color: 'blue' },
  IN_PROGRESS: { text: '进行中', color: 'processing' },
  COMPLETED: { text: '已完成', color: 'success' },
  CANCELLED: { text: '已取消', color: 'default' },
  NO_SHOW: { text: '未到场', color: 'error' },
};

/**
 * 支付状态映射
 */
export const PAYMENT_STATUS_MAP = {
  PENDING: { text: '待支付', color: 'orange' },
  PARTIAL: { text: '部分支付', color: 'processing' },
  PAID: { text: '已支付', color: 'success' },
  REFUNDED: { text: '已退款', color: 'default' },
  FAILED: { text: '支付失败', color: 'error' },
};

/**
 * 获取状态标签配置
 */
export const getStatusConfig = (status: string) => {
  return BOOKING_STATUS_MAP[status as keyof typeof BOOKING_STATUS_MAP] || { text: status, color: 'default' };
};

/**
 * 获取支付状态标签配置
 */
export const getPaymentStatusConfig = (status: string) => {
  return PAYMENT_STATUS_MAP[status as keyof typeof PAYMENT_STATUS_MAP] || { text: status, color: 'default' };
};

/**
 * 格式化金额
 */
export const formatAmount = (amount: number) => {
  return `¥${amount.toFixed(2)}`;
};

/**
 * 格式化日期时间
 */
export const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

/**
 * 格式化日期
 */
export const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString('zh-CN');
};
