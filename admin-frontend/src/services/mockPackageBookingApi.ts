import { PackageBooking, BookingSearchParams, BookingStatistics } from './packageBookingApi';

// 模拟数据
const mockBookings: PackageBooking[] = [
  {
    id: 1,
    bookingNumber: 'PB202509061001',
    userId: 1,
    userName: '张三',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-10',
    bookingTime: '09:00',
    participantCount: 2,
    unitPrice: 399,
    totalAmount: 798,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '张三',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望安排中文导游',
    participantInfo: '张三（成人）、李四（成人）',
    createdAt: '2025-09-06T10:30:00',
    updatedAt: '2025-09-06T10:30:00',
  },
  {
    id: 2,
    bookingNumber: 'PB202509061002',
    userId: 2,
    userName: '李四',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-12',
    bookingTime: '14:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '李四',
    contactPhone: '13800138002',
    contactEmail: '<EMAIL>',
    specialRequirements: '对海鲜过敏',
    participantInfo: '李四（成人）',
    confirmedAt: '2025-09-06T11:00:00',
    createdAt: '2025-09-06T09:15:00',
    updatedAt: '2025-09-06T11:00:00',
  },
  {
    id: 3,
    bookingNumber: 'PB202509061003',
    userId: 3,
    userName: '王五',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-15',
    bookingTime: '06:00',
    participantCount: 4,
    unitPrice: 899,
    totalAmount: 3596,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '王五',
    contactPhone: '13800138003',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要专业摄影指导',
    participantInfo: '王五（成人）、赵六（成人）、孙七（成人）、周八（成人）',
    createdAt: '2025-09-06T08:45:00',
    updatedAt: '2025-09-06T08:45:00',
  },
  {
    id: 4,
    bookingNumber: 'PB202509061004',
    userId: 4,
    userName: '赵六',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-08',
    bookingTime: '09:00',
    participantCount: 1,
    unitPrice: 399,
    totalAmount: 399,
    status: 'COMPLETED',
    paymentStatus: 'PAID',
    contactName: '赵六',
    contactPhone: '13800138004',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '赵六（成人）',
    confirmedAt: '2025-09-05T14:00:00',
    createdAt: '2025-09-05T10:20:00',
    updatedAt: '2025-09-08T18:00:00',
  },
  {
    id: 5,
    bookingNumber: 'PB202509061005',
    userId: 5,
    userName: '孙七',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-07',
    bookingTime: '14:00',
    participantCount: 2,
    unitPrice: 299,
    totalAmount: 598,
    status: 'CANCELLED',
    paymentStatus: 'REFUNDED',
    contactName: '孙七',
    contactPhone: '13800138005',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '孙七（成人）、周八（成人）',
    cancelledAt: '2025-09-06T12:00:00',
    cancellationReason: '行程变更，无法参加',
    createdAt: '2025-09-05T16:30:00',
    updatedAt: '2025-09-06T12:00:00',
  },
  // 添加更多测试数据
  {
    id: 6,
    bookingNumber: 'PB202509061006',
    userId: 6,
    userName: '周八',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '06:00',
    participantCount: 2,
    unitPrice: 899,
    totalAmount: 1798,
    status: 'IN_PROGRESS',
    paymentStatus: 'PAID',
    contactName: '周八',
    contactPhone: '13800138006',
    contactEmail: '<EMAIL>',
    specialRequirements: '需要高原反应药物',
    participantInfo: '周八（成人）、钱九（成人）',
    confirmedAt: '2025-09-06T08:00:00',
    createdAt: '2025-09-06T07:30:00',
    updatedAt: '2025-09-06T08:00:00',
  },
  {
    id: 7,
    bookingNumber: 'PB202509061007',
    userId: 7,
    userName: '钱九',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '14:00',
    participantCount: 3,
    unitPrice: 399,
    totalAmount: 1197,
    status: 'PENDING',
    paymentStatus: 'PENDING',
    contactName: '钱九',
    contactPhone: '13800138007',
    contactEmail: '<EMAIL>',
    specialRequirements: '希望有儿童适合的活动',
    participantInfo: '钱九（成人）、孙十（成人）、小明（儿童）',
    createdAt: '2025-09-06T13:15:00',
    updatedAt: '2025-09-06T13:15:00',
  },
  {
    id: 8,
    bookingNumber: 'PB202509061008',
    userId: 8,
    userName: '孙十',
    packageId: 2,
    packageName: '藏式美食烹饪体验课',
    packageLocation: '拉萨老城区',
    bookingDate: '2025-09-06', // 今日预订
    bookingTime: '16:00',
    participantCount: 1,
    unitPrice: 299,
    totalAmount: 299,
    status: 'CONFIRMED',
    paymentStatus: 'PARTIAL',
    contactName: '孙十',
    contactPhone: '13800138008',
    contactEmail: '<EMAIL>',
    specialRequirements: '素食主义者',
    participantInfo: '孙十（成人）',
    confirmedAt: '2025-09-06T14:30:00',
    createdAt: '2025-09-06T14:00:00',
    updatedAt: '2025-09-06T14:30:00',
  },
  {
    id: 9,
    bookingNumber: 'PB202509061009',
    userId: 9,
    userName: '李十一',
    packageId: 3,
    packageName: '高原摄影之旅',
    packageLocation: '稻城亚丁',
    bookingDate: '2025-09-20',
    bookingTime: '06:00',
    participantCount: 1,
    unitPrice: 899,
    totalAmount: 899,
    status: 'NO_SHOW',
    paymentStatus: 'PAID',
    contactName: '李十一',
    contactPhone: '13800138009',
    contactEmail: '<EMAIL>',
    specialRequirements: '',
    participantInfo: '李十一（成人）',
    confirmedAt: '2025-09-05T10:00:00',
    createdAt: '2025-09-05T09:30:00',
    updatedAt: '2025-09-20T06:30:00',
  },
  {
    id: 10,
    bookingNumber: 'PB202509061010',
    userId: 10,
    userName: '王十二',
    packageId: 1,
    packageName: '藏族传统手工艺体验',
    packageLocation: '康定市藏族文化中心',
    bookingDate: '2025-09-25',
    bookingTime: '09:00',
    participantCount: 4,
    unitPrice: 399,
    totalAmount: 1596,
    status: 'CONFIRMED',
    paymentStatus: 'PAID',
    contactName: '王十二',
    contactPhone: '13800138010',
    contactEmail: '<EMAIL>',
    specialRequirements: '团体预订，需要团体优惠',
    participantInfo: '王十二（成人）、张十三（成人）、李十四（成人）、赵十五（成人）',
    confirmedAt: '2025-09-06T15:00:00',
    createdAt: '2025-09-06T14:45:00',
    updatedAt: '2025-09-06T15:00:00',
  },
];

// 动态计算统计数据
const calculateStatistics = (): BookingStatistics => {
  console.log('🔄 重新计算统计数据...');

  const statusCounts = {
    PENDING: 0,
    CONFIRMED: 0,
    IN_PROGRESS: 0,
    COMPLETED: 0,
    CANCELLED: 0,
    NO_SHOW: 0,
  };

  const paymentStatusCounts = {
    PENDING: 0,
    PARTIAL: 0,
    PAID: 0,
    REFUNDED: 0,
    FAILED: 0,
  };

  const revenueByStatus = {
    PENDING: 0,
    CONFIRMED: 0,
    IN_PROGRESS: 0,
    COMPLETED: 0,
    CANCELLED: 0,
    NO_SHOW: 0,
  };

  let totalRevenue = 0;
  let paidRevenue = 0;
  let pendingRevenue = 0;
  let refundedRevenue = 0;
  let pendingBookings = 0;
  let todayBookings = 0;

  // 修复日期处理 - 使用固定的测试日期以确保一致性
  // 在生产环境中，这应该是当前日期
  const today = '2025-09-06'; // 固定测试日期，确保与测试数据一致
  const currentDate = new Date().toISOString().split('T')[0];

  console.log(`📅 今日统计使用日期: ${today} (当前系统日期: ${currentDate})`);

  mockBookings.forEach(booking => {
    // 统计预订状态
    statusCounts[booking.status]++;

    // 统计支付状态
    paymentStatusCounts[booking.paymentStatus]++;

    // 按状态统计收入
    revenueByStatus[booking.status] += booking.totalAmount;

    // 计算总收入（所有预订的金额）
    totalRevenue += booking.totalAmount;

    // 按支付状态计算收入 - 修复PARTIAL状态处理
    if (booking.paymentStatus === 'PAID') {
      paidRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'PENDING') {
      pendingRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'REFUNDED') {
      refundedRevenue += booking.totalAmount;
    } else if (booking.paymentStatus === 'PARTIAL') {
      // PARTIAL状态的预订，假设已支付一半
      const partialPaid = booking.totalAmount * 0.5;
      const partialPending = booking.totalAmount * 0.5;
      paidRevenue += partialPaid;
      pendingRevenue += partialPending;
      console.log(`💰 PARTIAL预订 ${booking.bookingNumber}: 总额¥${booking.totalAmount}, 已付¥${partialPaid}, 待付¥${partialPending}`);
    }

    // 统计待确认预订
    if (booking.status === 'PENDING') {
      pendingBookings++;
    }

    // 统计今日预订（按预订日期）
    if (booking.bookingDate === today) {
      todayBookings++;
      console.log(`📅 今日预订: ${booking.bookingNumber} (${booking.bookingDate})`);
    }
  });

  // 数据验证
  const calculatedTotal = paidRevenue + pendingRevenue + refundedRevenue;
  const totalDifference = Math.abs(calculatedTotal - totalRevenue);

  if (totalDifference > 0.01) {
    console.warn(`⚠️ 收入计算不一致: 总预订金额¥${totalRevenue.toFixed(2)}, 各状态总和¥${calculatedTotal.toFixed(2)}, 差异¥${totalDifference.toFixed(2)}`);
  }

  const statistics = {
    statusCounts,
    paymentStatusCounts,
    totalBookings: mockBookings.length,
    pendingBookings,
    todayBookings,
    monthlyRevenue: paidRevenue, // 本月收入使用已支付的金额
    totalRevenue,
    pendingRevenue,
    refundedRevenue,
    revenueByStatus,
  };

  console.log('📊 统计结果:', {
    totalBookings: statistics.totalBookings,
    pendingBookings: statistics.pendingBookings,
    todayBookings: statistics.todayBookings,
    totalRevenue: statistics.totalRevenue,
    monthlyRevenue: statistics.monthlyRevenue,
    pendingRevenue: statistics.pendingRevenue,
    refundedRevenue: statistics.refundedRevenue,
    revenueRatio: statistics.totalRevenue > 0 ? ((statistics.monthlyRevenue / statistics.totalRevenue) * 100).toFixed(1) + '%' : '0%'
  });

  return statistics;
};

const mockStatistics: BookingStatistics = calculateStatistics();

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 模拟文化套餐预订管理API
 */
export const mockPackageBookingApi = {
  /**
   * 获取所有预订列表
   */
  async getBookings(params: BookingSearchParams = {}) {
    await delay(500);
    
    let filteredBookings = [...mockBookings];
    
    // 状态筛选
    if (params.status) {
      filteredBookings = filteredBookings.filter(booking => booking.status === params.status);
    }
    
    // 支付状态筛选
    if (params.paymentStatus) {
      filteredBookings = filteredBookings.filter(booking => booking.paymentStatus === params.paymentStatus);
    }
    
    // 关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredBookings = filteredBookings.filter(booking => 
        booking.bookingNumber.toLowerCase().includes(keyword) ||
        booking.contactName.toLowerCase().includes(keyword) ||
        booking.contactPhone.includes(keyword) ||
        booking.packageName.toLowerCase().includes(keyword)
      );
    }
    
    // 日期筛选
    if (params.startDate) {
      filteredBookings = filteredBookings.filter(booking => 
        booking.bookingDate >= params.startDate!
      );
    }
    
    if (params.endDate) {
      filteredBookings = filteredBookings.filter(booking => 
        booking.bookingDate <= params.endDate!
      );
    }
    
    // 分页
    const page = params.page || 0;
    const size = params.size || 20;
    const start = page * size;
    const end = start + size;
    const content = filteredBookings.slice(start, end);
    
    return {
      data: {
        content,
        totalElements: filteredBookings.length,
        totalPages: Math.ceil(filteredBookings.length / size),
        size,
        number: page,
      },
    };
  },

  /**
   * 搜索预订
   */
  async searchBookings(params: BookingSearchParams) {
    return this.getBookings(params);
  },

  /**
   * 获取预订详情
   */
  async getBookingById(id: number) {
    await delay(300);
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error('预订不存在');
    }
    return { data: booking };
  },

  /**
   * 根据预订号获取预订详情
   */
  async getBookingByNumber(bookingNumber: string) {
    await delay(300);
    const booking = mockBookings.find(b => b.bookingNumber === bookingNumber);
    if (!booking) {
      throw new Error('预订不存在');
    }
    return { data: booking };
  },

  /**
   * 确认预订
   */
  async confirmBooking(id: number, data: { remarks?: string } = {}) {
    await delay(500);
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error('预订不存在');
    }
    if (booking.status !== 'PENDING') {
      throw new Error('只能确认待确认状态的预订');
    }
    
    booking.status = 'CONFIRMED';
    booking.confirmedAt = new Date().toISOString();
    booking.updatedAt = new Date().toISOString();

    if (data.remarks) {
      booking.specialRequirements = (booking.specialRequirements || '') + '\n管理员备注: ' + data.remarks;
    }

    // 重新计算统计数据
    Object.assign(mockStatistics, calculateStatistics());
    
    return { data: booking };
  },

  /**
   * 拒绝预订
   */
  async rejectBooking(id: number, data: { reason: string }) {
    await delay(500);
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error('预订不存在');
    }
    if (booking.status !== 'PENDING') {
      throw new Error('只能拒绝待确认状态的预订');
    }
    
    booking.status = 'CANCELLED';
    booking.cancelledAt = new Date().toISOString();
    booking.cancellationReason = data.reason;
    booking.updatedAt = new Date().toISOString();

    // 重新计算统计数据
    Object.assign(mockStatistics, calculateStatistics());
    
    return { data: booking };
  },

  /**
   * 取消预订
   */
  async cancelBooking(id: number, data: { reason?: string } = {}) {
    await delay(500);
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error('预订不存在');
    }
    if (booking.status === 'CANCELLED' || booking.status === 'COMPLETED') {
      throw new Error('预订已取消或已完成，无法再次取消');
    }
    
    booking.status = 'CANCELLED';
    booking.cancelledAt = new Date().toISOString();
    booking.cancellationReason = data.reason || '管理员取消';
    booking.updatedAt = new Date().toISOString();

    // 重新计算统计数据
    Object.assign(mockStatistics, calculateStatistics());
    
    return { data: booking };
  },

  /**
   * 完成预订
   */
  async completeBooking(id: number) {
    await delay(500);
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error('预订不存在');
    }
    if (booking.status !== 'CONFIRMED' && booking.status !== 'IN_PROGRESS') {
      throw new Error('只能完成已确认或进行中的预订');
    }
    
    booking.status = 'COMPLETED';
    booking.updatedAt = new Date().toISOString();

    // 重新计算统计数据
    Object.assign(mockStatistics, calculateStatistics());
    
    return { data: booking };
  },

  /**
   * 获取预订统计信息
   */
  async getBookingStatistics() {
    await delay(300);
    return { data: mockStatistics };
  },
};
