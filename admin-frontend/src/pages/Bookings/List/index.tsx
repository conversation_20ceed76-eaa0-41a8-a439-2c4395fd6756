import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Button,
  Space,
  Tag,
  Avatar,
  Dropdown,
  Modal,
  App,
  Row,
  Col,
  Select,
  DatePicker,
  Tooltip,
  Card,
  Statistic,
} from 'antd';
import {
  CalendarOutlined,
  EditOutlined,
  EyeOutlined,
  MoreOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  HomeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined,
  ExportOutlined,
  TrophyOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import AdminTable from '@/components/AdminTable';
import { useTable } from '@/hooks/useTable';
import { adminApi } from '@/services/admin';
import { Booking } from '@/types/admin';
import { notify } from '@/utils/notification';
import './index.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const BookingList: React.FC = () => {
  const navigate = useNavigate();
  const { modal } = App.useApp();
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [statistics, setStatistics] = useState({
    totalBookings: 0,
    totalRevenue: 0,
    confirmedBookings: 0,
    cancelledBookings: 0,
  });

  // 使用自定义表格Hook
  const {
    data: bookings,
    loading,
    pagination,
    selectedRowKeys,
    setSelectedRowKeys,
    handleTableChange,
    refresh,
    search,
  } = useTable<Booking>({
    apiCall: adminApi.getBookings,
    defaultPageSize: 20,
  });

  // 预订状态配置
  const bookingStatusConfig = {
    PENDING: { color: 'warning', text: '待确认' },
    CONFIRMED: { color: 'success', text: '已确认' },
    CHECKED_IN: { color: 'processing', text: '已入住' },
    CHECKED_OUT: { color: 'default', text: '已退房' },
    CANCELLED: { color: 'error', text: '已取消' },
    NO_SHOW: { color: 'error', text: '未到店' },
  };

  // 支付状态配置
  const paymentStatusConfig = {
    PENDING: { color: 'warning', text: '待支付' },
    PARTIAL: { color: 'processing', text: '部分支付' },
    PAID: { color: 'success', text: '已支付' },
    REFUNDED: { color: 'default', text: '已退款' },
    FAILED: { color: 'error', text: '支付失败' },
  };

  // 计算统计数据
  const calculateStatistics = (bookings: Booking[]) => {
    const stats = {
      totalBookings: bookings.length,
      totalRevenue: 0,
      confirmedBookings: 0,
      cancelledBookings: 0,
    };

    bookings.forEach(booking => {
      // 计算总收入（只计算已支付的预订）
      if (booking.paymentStatus === 'PAID') {
        stats.totalRevenue += booking.totalAmount || 0;
      }

      // 计算确认的预订数量
      if (['CONFIRMED', 'CHECKED_IN', 'CHECKED_OUT'].includes(booking.status)) {
        stats.confirmedBookings++;
      }

      // 计算取消的预订数量
      if (['CANCELLED', 'NO_SHOW'].includes(booking.status)) {
        stats.cancelledBookings++;
      }
    });

    return stats;
  };

  // 当预订数据变化时更新统计
  React.useEffect(() => {
    if (bookings) {
      const stats = calculateStatistics(bookings);
      setStatistics(stats);
    }
  }, [bookings]);

  // 表格列定义
  const columns: ColumnsType<Booking> = [
    {
      title: '预订信息',
      dataIndex: 'bookingNumber',
      key: 'bookingNumber',
      width: 200,
      render: (text, record) => (
        <div className="booking-info-cell">
          <Avatar
            size={40}
            icon={<CalendarOutlined />}
            style={{ backgroundColor: '#1890ff' }}
          />
          <div className="booking-details">
            <div className="booking-number">{text}</div>
            <div className="booking-date">
              {dayjs(record.createdAt).format('YYYY-MM-DD HH:mm')}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '客户信息',
      key: 'user',
      width: 150,
      render: (_, record) => (
        <div className="user-info">
          <Avatar size={32} icon={<UserOutlined />} />
          <div className="user-details">
            <div className="user-name">{record.userName || record.guestName || '未提供'}</div>
            <div className="user-phone">{record.guestPhone || '未提供'}</div>
          </div>
        </div>
      ),
    },
    {
      title: '酒店房间',
      key: 'hotel',
      width: 200,
      render: (_, record) => (
        <div className="hotel-room-info">
          <div className="hotel-name">
            <HomeOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {record.hotelName || '未知酒店'}
          </div>
          <div className="room-info">
            房间: {record.roomNumber || record.roomTypeName || '未知房间'}
          </div>
        </div>
      ),
    },
    {
      title: '入住日期',
      dataIndex: 'checkInDate',
      key: 'checkInDate',
      width: 120,
      render: (date, record) => (
        <div className="date-info">
          <div className="check-in">入住: {dayjs(date).format('MM-DD')}</div>
          <div className="check-out">退房: {dayjs(record.checkOutDate).format('MM-DD')}</div>
          <div className="nights">
            {dayjs(record.checkOutDate).diff(dayjs(date), 'day')} 晚
          </div>
        </div>
      ),
      sorter: true,
    },
    {
      title: '金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount) => (
        <div className="amount-info">
          <div className="total-amount">¥{amount?.toLocaleString() || 0}</div>
        </div>
      ),
      sorter: true,
    },
    {
      title: '预订状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = bookingStatusConfig[status as keyof typeof bookingStatusConfig];
        return (
          <Tag color={config?.color || 'default'}>
            {config?.text || status}
          </Tag>
        );
      },
      filters: [
        { text: '待确认', value: 'PENDING' },
        { text: '已确认', value: 'CONFIRMED' },
        { text: '已入住', value: 'CHECKED_IN' },
        { text: '已退房', value: 'CHECKED_OUT' },
        { text: '已取消', value: 'CANCELLED' },
        { text: '未到店', value: 'NO_SHOW' },
      ],
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status) => {
        const config = paymentStatusConfig[status as keyof typeof paymentStatusConfig];
        return (
          <Tag color={config?.color || 'default'}>
            {config?.text || status}
          </Tag>
        );
      },
      filters: [
        { text: '待支付', value: 'PENDING' },
        { text: '部分支付', value: 'PARTIAL' },
        { text: '已支付', value: 'PAID' },
        { text: '已退款', value: 'REFUNDED' },
        { text: '支付失败', value: 'FAILED' },
      ],
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: '查看详情',
                onClick: () => handleView(record.id),
              },
              {
                type: 'divider',
              },
              ...(record.status === 'PENDING' ? [
                {
                  key: 'confirm',
                  icon: <CheckCircleOutlined />,
                  label: '确认预订',
                  onClick: () => handleConfirm(record.id),
                },
                {
                  key: 'cancel',
                  icon: <CloseCircleOutlined />,
                  label: '取消预订',
                  onClick: () => handleCancel(record.id),
                },
              ] : []),
              ...(record.status === 'CONFIRMED' && record.paymentStatus === 'PAID' ? [
                {
                  key: 'checkin',
                  label: '办理入住',
                  onClick: () => handleCheckIn(record.id),
                },
                {
                  key: 'checkout',
                  label: '办理退房',
                  onClick: () => handleCheckOut(record.id),
                },
              ] : []),
              ...(record.paymentStatus === 'PAID' && record.status === 'CANCELLED' ? [
                {
                  key: 'refund',
                  icon: <DollarOutlined />,
                  label: '处理退款',
                  onClick: () => handleRefund(record.id),
                },
              ] : []),
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // 处理查看详情
  const handleView = (id: number) => {
    navigate(`/admin/bookings/view/${id}`);
  };

  // 处理确认预订
  const handleConfirm = (id: number) => {
    modal.confirm({
      title: '确认预订',
      icon: <CheckCircleOutlined />,
      content: '确定要确认这个预订吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await adminApi.operateBooking(id, {
            action: 'confirm',
            reason: '管理员确认',
          });
          notify.success('预订确认成功');
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '预订确认失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 处理取消预订
  const handleCancel = (id: number) => {
    modal.confirm({
      title: '取消预订',
      icon: <ExclamationCircleOutlined />,
      content: '确定要取消这个预订吗？请输入取消原因：',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await adminApi.operateBooking(id, {
            action: 'cancel',
            reason: '管理员取消',
          });
          notify.success('预订取消成功');
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '预订取消失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 处理入住
  const handleCheckIn = (id: number) => {
    modal.confirm({
      title: '办理入住',
      content: '确定要为客户办理入住手续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await adminApi.operateBooking(id, {
            action: 'checkin',
          });
          notify.success('入住办理成功');
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '入住办理失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 处理退房
  const handleCheckOut = (id: number) => {
    modal.confirm({
      title: '办理退房',
      content: '确定要为客户办理退房手续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await adminApi.operateBooking(id, {
            action: 'checkout',
          });
          notify.success('退房办理成功');
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '退房办理失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 处理退款
  const handleRefund = (id: number) => {
    modal.confirm({
      title: '处理退款',
      content: '确定要处理退款吗？退款金额将按照取消政策计算。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await adminApi.operateBooking(id, {
            action: 'modify',
            reason: '管理员处理退款',
          });
          notify.success('退款处理成功');
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '退款处理失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    search({ search: value });
  };

  // 批量操作处理函数
  const handleBatchOperation = (operation: string) => {
    if (selectedRowKeys.length === 0) {
      notify.warning('请先选择要操作的预订');
      return;
    }

    const selectedBookings = bookings.filter(booking =>
      selectedRowKeys.includes(booking.id)
    );

    switch (operation) {
      case 'confirm':
        handleBatchConfirm(selectedBookings);
        break;
      case 'cancel':
        handleBatchCancel(selectedBookings);
        break;
      case 'export':
        handleBatchExport(selectedBookings);
        break;
      default:
        notify.error('不支持的批量操作');
    }
  };

  // 批量确认
  const handleBatchConfirm = (bookings: Booking[]) => {
    const pendingBookings = bookings.filter(b => b.status === 'PENDING');
    if (pendingBookings.length === 0) {
      notify.warning('所选预订中没有待确认的预订');
      return;
    }

    modal.confirm({
      title: '批量确认预订',
      content: `确定要确认 ${pendingBookings.length} 个预订吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const promises = pendingBookings.map(booking =>
            adminApi.operateBooking(booking.id, {
              action: 'confirm',
              reason: '批量确认',
            })
          );
          await Promise.all(promises);
          notify.success(`成功确认 ${pendingBookings.length} 个预订`);
          setSelectedRowKeys([]);
          refresh();
        } catch (error: any) {
          // 显示具体的错误信息
          const errorMessage = error?.message || '批量确认失败';
          notify.error(errorMessage);
        }
      },
    });
  };

  // 批量取消
  const handleBatchCancel = (bookings: Booking[]) => {
    const cancelableBookings = bookings.filter(b =>
      b.status === 'PENDING' || b.status === 'CONFIRMED'
    );
    if (cancelableBookings.length === 0) {
      notify.warning('所选预订中没有可取消的预订');
      return;
    }

    modal.confirm({
      title: '批量取消预订',
      content: `确定要取消 ${cancelableBookings.length} 个预订吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const promises = cancelableBookings.map(booking =>
            adminApi.operateBooking(booking.id, {
              action: 'cancel',
              reason: '批量取消',
            })
          );
          await Promise.all(promises);
          notify.success(`成功取消 ${cancelableBookings.length} 个预订`);
          setSelectedRowKeys([]);
          refresh();
        } catch (error) {
          notify.error('批量取消失败');
        }
      },
    });
  };

  // 批量导出
  const handleBatchExport = (bookings: Booking[]) => {
    try {
      const csvContent = generateCSV(bookings);
      downloadCSV(csvContent, `预订数据_${new Date().toISOString().split('T')[0]}.csv`);
      notify.success(`成功导出 ${bookings.length} 条预订数据`);
    } catch (error) {
      notify.error('导出失败');
    }
  };

  // 生成CSV内容
  const generateCSV = (bookings: Booking[]) => {
    const headers = [
      '预订编号', '客户姓名', '联系电话', '酒店名称', '房型',
      '入住日期', '退房日期', '住宿天数', '总金额', '预订状态', '支付状态'
    ];

    const rows = bookings.map(booking => [
      booking.bookingNumber || '',
      booking.user?.fullName || '',
      booking.user?.phone || '',
      booking.hotel?.name || '',
      booking.roomType?.name || '',
      booking.checkInDate || '',
      booking.checkOutDate || '',
      booking.totalNights || '',
      booking.totalAmount || '',
      bookingStatusConfig[booking.status as keyof typeof bookingStatusConfig]?.text || booking.status,
      paymentStatusConfig[booking.paymentStatus as keyof typeof paymentStatusConfig]?.text || booking.paymentStatus
    ]);

    return [headers, ...rows].map(row =>
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n');
  };

  // 下载CSV文件
  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 处理筛选
  const handleFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    search({ ...filters, [key]: value });
  };

  // 处理日期筛选
  const handleDateFilter = (dates: any) => {
    if (dates && dates.length === 2) {
      handleFilter('dateFrom', dates[0].format('YYYY-MM-DD'));
      handleFilter('dateTo', dates[1].format('YYYY-MM-DD'));
    } else {
      handleFilter('dateFrom', undefined);
      handleFilter('dateTo', undefined);
    }
  };

  // 筛选器组件
  const renderFilters = () => (
    <>
      <Row gutter={16}>
        <Col span={6}>
          <Select
            placeholder="预订状态"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('status', value)}
          >
            <Option value="PENDING">待确认</Option>
            <Option value="CONFIRMED">已确认</Option>
            <Option value="CHECKED_IN">已入住</Option>
            <Option value="CHECKED_OUT">已退房</Option>
            <Option value="CANCELLED">已取消</Option>
            <Option value="NO_SHOW">未到店</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder="支付状态"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('paymentStatus', value)}
          >
            <Option value="PENDING">待支付</Option>
            <Option value="PARTIAL">部分支付</Option>
            <Option value="PAID">已支付</Option>
            <Option value="REFUNDED">已退款</Option>
            <Option value="FAILED">支付失败</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder="选择酒店"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('hotelId', value)}
          >
            {/* 这里应该从API获取酒店列表 */}
            <Option value={1}>康定情歌大酒店</Option>
            <Option value={2}>稻城亚丁度假村</Option>
          </Select>
        </Col>
        <Col span={6}>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            style={{ width: '100%' }}
            onChange={handleDateFilter}
          />
        </Col>
      </Row>
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Select
            placeholder="金额范围"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('amountRange', value)}
          >
            <Option value="0-500">0-500元</Option>
            <Option value="500-1000">500-1000元</Option>
            <Option value="1000-2000">1000-2000元</Option>
            <Option value="2000-5000">2000-5000元</Option>
            <Option value="5000+">5000元以上</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder="入住天数"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('nightsRange', value)}
          >
            <Option value="1">1晚</Option>
            <Option value="2-3">2-3晚</Option>
            <Option value="4-7">4-7晚</Option>
            <Option value="7+">7晚以上</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Select
            placeholder="预订来源"
            allowClear
            style={{ width: '100%' }}
            onChange={(value) => handleFilter('source', value)}
          >
            <Option value="WEB">官网预订</Option>
            <Option value="MOBILE">手机预订</Option>
            <Option value="PHONE">电话预订</Option>
            <Option value="WALK_IN">现场预订</Option>
          </Select>
        </Col>
        <Col span={6}>
          <Button
            type="primary"
            onClick={() => {
              // 重置所有筛选条件
              setFilters({});
              refresh();
            }}
            style={{ width: '100%' }}
          >
            重置筛选
          </Button>
        </Col>
      </Row>
    </>
  );

  return (
    <div className="booking-list fade-in">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="flex-between">
          <div>
            <Title level={2} className="page-header-title">
              <CalendarOutlined style={{ marginRight: 8 }} />
              预订管理
            </Title>
            <Text className="page-header-subtitle">
              管理系统中的所有预订信息，共 {pagination.total} 个预订
            </Text>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总预订数"
              value={statistics.totalBookings}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收入"
              value={statistics.totalRevenue}
              prefix={<DollarOutlined />}
              suffix="元"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="确认预订"
              value={statistics.confirmedBookings}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="取消预订"
              value={statistics.cancelledBookings}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 表格 */}
      <AdminTable
        columns={columns}
        dataSource={bookings}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="id"
        selectedRowKeys={selectedRowKeys}
        onSelectChange={setSelectedRowKeys}
        showSearch
        searchPlaceholder="搜索预订号、客户姓名、酒店名称"
        onSearch={handleSearch}
        showFilters
        filters={renderFilters()}
        actions={{
          refresh: {
            onClick: refresh,
          },
          export: {
            onClick: () => {
              // 如果没有选择任何预订，导出所有预订
              if (selectedRowKeys.length === 0) {
                handleBatchExport(bookings);
              } else {
                handleBatchOperation('export');
              }
            },
          },
          batchDelete: selectedRowKeys.length > 0 ? {
            text: '批量操作',
            onClick: (selectedKeys) => {
              // 显示批量操作菜单
              modal.confirm({
                title: '批量操作',
                content: (
                  <div>
                    <p>已选择 {selectedKeys.length} 个预订，请选择要执行的操作：</p>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        type="primary"
                        icon={<CheckCircleOutlined />}
                        onClick={() => {
                          handleBatchOperation('confirm');
                        }}
                        block
                      >
                        批量确认
                      </Button>
                      <Button
                        danger
                        icon={<CloseCircleOutlined />}
                        onClick={() => {
                          handleBatchOperation('cancel');
                        }}
                        block
                      >
                        批量取消
                      </Button>
                      <Button
                        icon={<ExportOutlined />}
                        onClick={() => {
                          handleBatchOperation('export');
                        }}
                        block
                      >
                        批量导出
                      </Button>
                    </Space>
                  </div>
                ),
                okText: '关闭',
                cancelText: null,
                onOk: () => {},
              });
            },
          } : undefined,
        }}
      />
    </div>
  );
};

export default BookingList;
