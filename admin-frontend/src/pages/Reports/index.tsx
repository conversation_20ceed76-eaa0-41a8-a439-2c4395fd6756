import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, Alert, Typography } from 'antd';
// 使用原生HTML/CSS实现图表，不依赖外部图表库
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  UserOutlined,
  HomeOutlined,
  CalendarOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { adminApi } from '@/services/admin';
import './index.css';

const { Title, Text } = Typography;

interface BookingTrendPoint {
  date: string;
  bookingCount: number;
  revenue: number;
}

interface RevenueDistribution {
  category: string;
  amount: number;
  percentage: number;
}

interface MetricCard {
  title: string;
  value: string;
  trend: string;
  trendType: 'up' | 'down' | 'stable';
}

interface DashboardData {
  bookingTrend: BookingTrendPoint[];
  revenueDistribution: RevenueDistribution[];
  metrics: MetricCard[];
  occupancyRate: number;
}

const Reports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 酒店收入颜色配置 - 确保颜色对比度足够，便于区分
  const hotelColors = [
    '#1890ff', // 蓝色 - 稻城亚丁香格里拉大酒店
    '#52c41a', // 绿色 - 康定情歌大酒店
    '#faad14', // 橙色 - 丹巴美人谷度假村
    '#f5222d', // 红色 - 泸定桥畔精品酒店
    '#722ed1', // 紫色 - 九龙山温泉酒店
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔍 开始获取仪表板数据...');
      const response = await adminApi.getDashboardData();
      console.log('📊 获取到的响应数据:', response);

      // 根据API修复后的数据结构处理
      let dashboardData = response;
      if (response && response.data) {
        dashboardData = response.data;
      }

      console.log('📈 处理后的仪表板数据:', dashboardData);
      setData(dashboardData);
    } catch (err: any) {
      console.error('❌ 获取仪表板数据失败:', err);
      setError(err.message || '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trendType: string) => {
    switch (trendType) {
      case 'up':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MinusOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getMetricIcon = (title: string) => {
    switch (title) {
      case '总用户数':
        return <UserOutlined />;
      case '总酒店数':
        return <HomeOutlined />;
      case '总预订数':
        return <CalendarOutlined />;
      case '本月收入':
        return <DollarOutlined />;
      default:
        return <CalendarOutlined />;
    }
  };

  // 图表配置已移除，使用原生HTML/CSS实现

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>正在加载数据...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button onClick={fetchDashboardData}>重试</button>
          }
        />
      </div>
    );
  }

  return (
    <div className="reports-container" style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div className="reports-header" style={{ marginBottom: '32px', textAlign: 'center' }}>
        <Title level={2} style={{ marginBottom: '8px', color: '#1890ff' }}>
          📊 数据可视化报表
        </Title>
        <Text type="secondary" style={{ fontSize: '16px' }}>
          实时业务数据概览与趋势分析
        </Text>
      </div>

      {/* 关键指标卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
        {data?.metrics?.map((metric, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card
              hoverable
              style={{
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                border: 'none'
              }}
              bodyStyle={{ padding: '24px' }}
            >
              <Statistic
                title={
                  <span style={{ fontSize: '14px', fontWeight: '500', color: '#666' }}>
                    {metric.title}
                  </span>
                }
                value={metric.value}
                valueStyle={{ fontSize: '28px', fontWeight: 'bold', color: '#1890ff' }}
                prefix={
                  <span style={{ fontSize: '20px', marginRight: '8px', color: '#1890ff' }}>
                    {getMetricIcon(metric.title)}
                  </span>
                }
                suffix={
                  <div style={{ fontSize: '14px', marginTop: '8px' }}>
                    {getTrendIcon(metric.trendType)}
                    <Text type="secondary" style={{ marginLeft: '6px', fontSize: '14px' }}>
                      {metric.trend}
                    </Text>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[24, 24]}>
        {/* 预订趋势图 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <span style={{ fontSize: '18px', fontWeight: '600', color: '#333' }}>
                📈 预订趋势（最近7天）
              </span>
            }
            style={{
              height: '450px',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              border: 'none'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <div style={{
              height: '350px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'space-around',
              padding: '20px 10px',
              borderBottom: '2px solid #f0f0f0',
              position: 'relative'
            }}>
              {data?.bookingTrend?.map((item, index) => {
                const maxValue = Math.max(...(data.bookingTrend?.map(d => d.bookingCount) || [1]));
                const height = maxValue > 0 ? (item.bookingCount / maxValue) * 250 : 2;
                return (
                  <div key={index} style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flex: 1,
                    position: 'relative'
                  }}>
                    <div
                      style={{
                        background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
                        width: '36px',
                        height: `${height}px`,
                        marginBottom: '8px',
                        borderRadius: '6px 6px 2px 2px',
                        minHeight: '4px',
                        boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.05)';
                        e.currentTarget.style.boxShadow = '0 4px 16px rgba(24, 144, 255, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.3)';
                      }}
                    >
                      <div style={{
                        position: 'absolute',
                        top: '-30px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#1890ff',
                        background: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
                      }}>
                        {item.bookingCount}
                      </div>
                    </div>
                    <div style={{
                      fontSize: '11px',
                      textAlign: 'center',
                      color: '#666',
                      marginTop: '8px',
                      fontWeight: '500'
                    }}>
                      {item.date.slice(5)}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </Col>

        {/* 入住率仪表盘 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <span style={{ fontSize: '18px', fontWeight: '600', color: '#333' }}>
                🏨 整体入住率
              </span>
            }
            style={{
              height: '450px',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              border: 'none'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <div style={{
              textAlign: 'center',
              paddingTop: '80px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%'
            }}>
              <div style={{
                width: '180px',
                height: '180px',
                borderRadius: '50%',
                background: `conic-gradient(#1890ff 0% ${data?.occupancyRate || 0}%, #f0f0f0 ${data?.occupancyRate || 0}% 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '20px',
                boxShadow: '0 8px 24px rgba(24, 144, 255, 0.2)'
              }}>
                <div style={{
                  width: '120px',
                  height: '120px',
                  borderRadius: '50%',
                  background: 'white',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                  <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#1890ff' }}>
                    {data?.occupancyRate?.toFixed(1) || 0}%
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px', marginTop: '4px' }}>
                    入住率
                  </Text>
                </div>
              </div>
              <Text type="secondary" style={{ fontSize: '16px' }}>
                当前整体入住率水平
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        {/* 收入分布柱状图 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <span style={{ fontSize: '18px', fontWeight: '600', color: '#333' }}>
                💰 酒店收入分布
              </span>
            }
            style={{
              height: '450px',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              border: 'none'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <div style={{
              height: '350px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              gap: '16px',
              padding: '20px 10px',
              borderBottom: '2px solid #f0f0f0'
            }}>
              {data?.revenueDistribution?.map((item, index) => {
                const maxValue = Math.max(...(data.revenueDistribution?.map(d => d.amount) || [1]));
                const height = maxValue > 0 ? (item.amount / maxValue) * 250 : 2;
                const color = hotelColors[index] || '#52c41a';
                return (
                  <div key={index} style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flex: 1,
                    maxWidth: '120px'
                  }}>
                    <div
                      style={{
                        background: `linear-gradient(135deg, ${color}, ${color}dd)`,
                        width: '60px',
                        height: `${height}px`,
                        marginBottom: '8px',
                        borderRadius: '6px 6px 2px 2px',
                        minHeight: '4px',
                        boxShadow: `0 2px 8px ${color}40`,
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'scale(1.05)';
                        e.currentTarget.style.boxShadow = `0 4px 16px ${color}60`;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'scale(1)';
                        e.currentTarget.style.boxShadow = `0 2px 8px ${color}40`;
                      }}
                    >
                      <div style={{
                        position: 'absolute',
                        top: '-35px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontSize: '11px',
                        fontWeight: 'bold',
                        color: color,
                        background: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
                        whiteSpace: 'nowrap'
                      }}>
                        ¥{item.amount.toLocaleString()}
                      </div>
                    </div>
                    <div style={{
                      fontSize: '10px',
                      textAlign: 'center',
                      color: '#666',
                      lineHeight: '1.2',
                      marginTop: '8px',
                      wordWrap: 'break-word',
                      maxWidth: '100px',
                      fontWeight: '500'
                    }}>
                      {item.category}
                    </div>
                    <div style={{
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: color,
                      marginTop: '4px'
                    }}>
                      {item.percentage}%
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </Col>

        {/* 收入分布饼图 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <span style={{ fontSize: '18px', fontWeight: '600', color: '#333' }}>
                🥧 收入占比分析
              </span>
            }
            style={{
              height: '450px',
              borderRadius: '12px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              border: 'none'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <div style={{
              height: '350px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '40px'
            }}>
              {/* 饼图 */}
              <div style={{
                width: '180px',
                height: '180px',
                borderRadius: '50%',
                background: data?.revenueDistribution?.length > 0
                  ? (() => {
                      let gradientStr = 'conic-gradient(';
                      let currentPercentage = 0;
                      data.revenueDistribution.forEach((item, index) => {
                        const color = hotelColors[index] || '#52c41a';
                        gradientStr += `${color} ${currentPercentage}% ${currentPercentage + item.percentage}%`;
                        currentPercentage += item.percentage;
                        if (index < data.revenueDistribution.length - 1) {
                          gradientStr += ', ';
                        }
                      });
                      gradientStr += ')';
                      return gradientStr;
                    })()
                  : '#1890ff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                transition: 'transform 0.3s ease',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
              >
                <div style={{
                  width: '100px',
                  height: '100px',
                  background: 'white',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: '#333',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                  收入占比
                </div>
              </div>

              {/* 图例 */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {data?.revenueDistribution?.map((item, index) => {
                  const color = hotelColors[index] || '#52c41a';
                  return (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px 12px',
                      background: '#fafafa',
                      borderRadius: '8px',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#f0f0f0';
                      e.currentTarget.style.transform = 'translateX(4px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = '#fafafa';
                      e.currentTarget.style.transform = 'translateX(0)';
                    }}
                    >
                      <div style={{
                        width: '16px',
                        height: '16px',
                        background: color,
                        marginRight: '12px',
                        borderRadius: '4px',
                        boxShadow: `0 2px 4px ${color}40`
                      }}></div>
                      <div style={{
                        fontSize: '13px',
                        color: '#333',
                        fontWeight: '500',
                        maxWidth: '140px',
                        lineHeight: '1.3'
                      }}>
                        <div>{item.category}</div>
                        <div style={{ color: color, fontWeight: 'bold', marginTop: '2px' }}>
                          {item.percentage}% (¥{item.amount.toLocaleString()})
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Reports;
