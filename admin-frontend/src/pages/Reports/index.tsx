import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, Alert, Typography } from 'antd';
// 使用原生HTML/CSS实现图表，不依赖外部图表库
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  UserOutlined,
  HomeOutlined,
  CalendarOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { adminApi } from '@/services/admin';
import './index.css';

const { Title, Text } = Typography;

interface BookingTrendPoint {
  date: string;
  bookingCount: number;
  revenue: number;
}

interface RevenueDistribution {
  category: string;
  amount: number;
  percentage: number;
}

interface MetricCard {
  title: string;
  value: string;
  trend: string;
  trendType: 'up' | 'down' | 'stable';
}

interface DashboardData {
  bookingTrend: BookingTrendPoint[];
  revenueDistribution: RevenueDistribution[];
  metrics: MetricCard[];
  occupancyRate: number;
}

const Reports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔍 开始获取仪表板数据...');
      const response = await adminApi.getDashboardData();
      console.log('📊 获取到的响应数据:', response);

      // 根据API修复后的数据结构处理
      let dashboardData = response;
      if (response && response.data) {
        dashboardData = response.data;
      }

      console.log('📈 处理后的仪表板数据:', dashboardData);
      setData(dashboardData);
    } catch (err: any) {
      console.error('❌ 获取仪表板数据失败:', err);
      setError(err.message || '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trendType: string) => {
    switch (trendType) {
      case 'up':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MinusOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getMetricIcon = (title: string) => {
    switch (title) {
      case '总用户数':
        return <UserOutlined />;
      case '总酒店数':
        return <HomeOutlined />;
      case '总预订数':
        return <CalendarOutlined />;
      case '本月收入':
        return <DollarOutlined />;
      default:
        return <CalendarOutlined />;
    }
  };

  // 图表配置已移除，使用原生HTML/CSS实现

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text>正在加载数据...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button onClick={fetchDashboardData}>重试</button>
          }
        />
      </div>
    );
  }

  return (
    <div className="reports-container">
      <div className="reports-header">
        <Title level={2}>数据可视化报表</Title>
        <Text type="secondary">实时业务数据概览与趋势分析</Text>
      </div>

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {data?.metrics?.map((metric, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={metric.title}
                value={metric.value}
                prefix={getMetricIcon(metric.title)}
                suffix={
                  <span style={{ fontSize: '14px', marginLeft: '8px' }}>
                    {getTrendIcon(metric.trendType)}
                    <Text type="secondary" style={{ marginLeft: '4px' }}>
                      {metric.trend}
                    </Text>
                  </span>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        {/* 预订趋势图 */}
        <Col xs={24} lg={12}>
          <Card title="预订趋势（最近7天）" style={{ height: '400px' }}>
            <div style={{ height: '300px', display: 'flex', alignItems: 'end', justifyContent: 'space-around', padding: '20px 10px' }}>
              {data?.bookingTrend?.map((item, index) => {
                const maxValue = Math.max(...(data.bookingTrend?.map(d => d.bookingCount) || [1]));
                const height = maxValue > 0 ? (item.bookingCount / maxValue) * 200 : 0;
                return (
                  <div key={index} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', flex: 1 }}>
                    <div style={{
                      background: '#1890ff',
                      width: '30px',
                      height: `${height}px`,
                      marginBottom: '5px',
                      borderRadius: '2px',
                      minHeight: '2px'
                    }}></div>
                    <div style={{ fontSize: '10px', textAlign: 'center', transform: 'rotate(-45deg)', marginTop: '10px' }}>
                      {item.date.slice(5)}
                    </div>
                    <div style={{ fontSize: '12px', fontWeight: 'bold', color: '#1890ff', marginTop: '15px' }}>
                      {item.bookingCount}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </Col>

        {/* 入住率仪表盘 */}
        <Col xs={24} lg={12}>
          <Card title="整体入住率" style={{ height: '400px' }}>
            <div style={{ textAlign: 'center', paddingTop: '60px' }}>
              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#1890ff' }}>
                {data?.occupancyRate?.toFixed(1) || 0}%
              </div>
              <Text type="secondary">当前入住率</Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        {/* 收入分布柱状图 */}
        <Col xs={24} lg={12}>
          <Card title="酒店收入分布" style={{ height: '400px' }}>
            <div style={{ height: '300px', display: 'flex', alignItems: 'end', justifyContent: 'center', gap: '20px', padding: '20px' }}>
              {data?.revenueDistribution?.map((item, index) => {
                const maxValue = Math.max(...(data.revenueDistribution?.map(d => d.amount) || [1]));
                const height = maxValue > 0 ? (item.amount / maxValue) * 200 : 0;
                return (
                  <div key={index} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <div style={{
                      background: '#52c41a',
                      width: '80px',
                      height: `${height}px`,
                      marginBottom: '5px',
                      borderRadius: '4px',
                      minHeight: '2px'
                    }}></div>
                    <div style={{ fontSize: '12px', textAlign: 'center', maxWidth: '100px', wordWrap: 'break-word' }}>
                      {item.category}
                    </div>
                    <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#52c41a', marginTop: '5px' }}>
                      ¥{item.amount}
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </Col>

        {/* 收入分布饼图 */}
        <Col xs={24} lg={12}>
          <Card title="收入占比分析" style={{ height: '400px' }}>
            <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '40px' }}>
                {/* 简单的饼图表示 */}
                <div style={{
                  width: '150px',
                  height: '150px',
                  borderRadius: '50%',
                  background: data?.revenueDistribution?.length > 1
                    ? `conic-gradient(#1890ff 0% ${data.revenueDistribution[0]?.percentage || 0}%, #52c41a ${data.revenueDistribution[0]?.percentage || 0}% 100%)`
                    : '#1890ff',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    background: 'white',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'bold',
                    fontSize: '12px'
                  }}>
                    收入占比
                  </div>
                </div>

                {/* 图例 */}
                <div>
                  {data?.revenueDistribution?.map((item, index) => {
                    const color = index === 0 ? '#1890ff' : '#52c41a';
                    return (
                      <div key={index} style={{ display: 'flex', alignItems: 'center', margin: '10px 0' }}>
                        <div style={{ width: '16px', height: '16px', background: color, marginRight: '8px', borderRadius: '2px' }}></div>
                        <div style={{ fontSize: '14px' }}>
                          {item.category}: {item.percentage}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Reports;
