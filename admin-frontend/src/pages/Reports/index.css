/* 报表容器样式 */
.reports-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.reports-header {
  margin-bottom: 32px;
  padding: 20px 0;
  text-align: center;
}

.reports-header h2 {
  margin-bottom: 8px;
  color: #1890ff;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .reports-container {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: 16px;
  }

  .reports-header h2 {
    font-size: 20px;
  }

  /* 移动端图表调整 */
  .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .reports-container {
    padding: 12px;
  }

  .reports-header {
    margin-bottom: 20px;
  }

  /* 小屏幕下的图表样式 */
  .ant-card {
    margin-bottom: 12px;
  }
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
}

.ant-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 统计数字样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.ant-statistic-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ant-statistic-content-value {
  font-weight: 700;
  font-size: 28px;
  color: #1890ff;
}

.ant-statistic-content-prefix {
  font-size: 20px;
  margin-right: 8px;
  color: #1890ff;
}

/* 图表容器样式 */
.ant-card .ant-card-body {
  padding: 24px;
}

/* 图表交互效果 */
.chart-bar {
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-bar:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* 图例样式 */
.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.legend-item:hover {
  background: #f0f0f0;
  transform: translateX(4px);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-right: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.legend-text {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

/* 加载状态样式 */
.ant-spin-container {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

/* 错误状态样式 */
.ant-alert {
  border-radius: 8px;
  border: none;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ant-card {
  animation: fadeInUp 0.6s ease-out;
}

.ant-card:nth-child(1) { animation-delay: 0.1s; }
.ant-card:nth-child(2) { animation-delay: 0.2s; }
.ant-card:nth-child(3) { animation-delay: 0.3s; }
.ant-card:nth-child(4) { animation-delay: 0.4s; }
