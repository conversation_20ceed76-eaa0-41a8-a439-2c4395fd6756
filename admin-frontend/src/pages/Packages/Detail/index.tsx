import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Space,
  Tag,
  Descriptions,
  Image,
  Row,
  Col,
  List,
  Divider,
  Spin,
  Alert,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  PoweroffOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { adminApi } from '@/services/admin';
import { CulturalPackage } from '@/types/admin';
import { notify } from '@/utils/notification';

const { Title, Text, Paragraph } = Typography;

const PackageDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [package_, setPackage] = useState<CulturalPackage | null>(null);
  const [loading, setLoading] = useState(true);

  // 套餐类别配置
  const categoryConfig = {
    TIBETAN_CULTURE: { color: 'red', text: '藏族文化' },
    RELIGIOUS_EXPERIENCE: { color: 'orange', text: '宗教体验' },
    NATURAL_SCENERY: { color: 'green', text: '自然风光' },
    TRADITIONAL_CRAFT: { color: 'blue', text: '传统工艺' },
    LOCAL_CUISINE: { color: 'purple', text: '当地美食' },
    FESTIVAL_CELEBRATION: { color: 'magenta', text: '节庆活动' },
    PILGRIMAGE_TOUR: { color: 'volcano', text: '朝圣之旅' },
    PHOTOGRAPHY_TOUR: { color: 'cyan', text: '摄影之旅' },
    ADVENTURE_SPORTS: { color: 'lime', text: '探险运动' },
    WELLNESS_RETREAT: { color: 'geekblue', text: '康养体验' },
  };

  // 难度等级配置
  const difficultyConfig = {
    EASY: { color: 'success', text: '简单' },
    MODERATE: { color: 'warning', text: '中等' },
    CHALLENGING: { color: 'error', text: '困难' },
    EXTREME: { color: 'error', text: '极限' },
  };

  // 获取套餐详情
  useEffect(() => {
    const fetchPackage = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const response = await adminApi.getPackage(Number(id));
        setPackage(response);
      } catch (error) {
        notify.error('获取套餐详情失败');
        navigate('/admin/packages');
      } finally {
        setLoading(false);
      }
    };

    fetchPackage();
  }, [id, navigate]);

  // 处理编辑
  const handleEdit = () => {
    navigate(`/admin/packages/edit/${id}`);
  };

  // 处理删除
  const handleDelete = async () => {
    if (!package_) return;
    
    try {
      await adminApi.deletePackage(package_.id);
      notify.success('套餐删除成功');
      navigate('/admin/packages');
    } catch (error) {
      notify.error('套餐删除失败');
    }
  };

  // 处理状态切换
  const handleToggleStatus = async () => {
    if (!package_) return;
    
    try {
      if (package_.isActive) {
        await adminApi.deactivatePackage(package_.id);
        notify.success('套餐已停用');
      } else {
        await adminApi.activatePackage(package_.id);
        notify.success('套餐已激活');
      }
      
      // 重新获取数据
      const response = await adminApi.getPackage(Number(id));
      setPackage(response.data);
    } catch (error) {
      notify.error('状态更新失败');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!package_) {
    return (
      <Alert
        message="套餐不存在"
        description="请检查套餐ID是否正确"
        type="error"
        showIcon
      />
    );
  }

  const getCategoryTag = (category: string) => {
    const config = categoryConfig[category as keyof typeof categoryConfig] || {
      color: 'default',
      text: category,
    };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getDifficultyTag = (difficulty: string) => {
    const config = difficultyConfig[difficulty as keyof typeof difficultyConfig] || {
      color: 'default',
      text: difficulty,
    };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  return (
    <div className="package-detail fade-in">
      <div className="page-header">
        <Title level={2} className="page-header-title">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/admin/packages')}
            style={{ marginRight: 16 }}
          />
          套餐详情
        </Title>
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEdit}
          >
            编辑
          </Button>
          <Button
            type={package_.isActive ? 'default' : 'primary'}
            icon={package_.isActive ? <PoweroffOutlined /> : <CheckCircleOutlined />}
            onClick={handleToggleStatus}
          >
            {package_.isActive ? '停用' : '启用'}
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={handleDelete}
          >
            删除
          </Button>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          <Card title="基本信息" className="mb-16">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="套餐名称" span={2}>
                <Text strong style={{ fontSize: 16 }}>
                  {package_.name}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="套餐ID">
                {package_.id}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={package_.isActive ? 'success' : 'default'}>
                  {package_.isActive ? '启用' : '停用'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="类别">
                {getCategoryTag(package_.category)}
              </Descriptions.Item>
              <Descriptions.Item label="难度等级">
                {getDifficultyTag(package_.difficultyLevel)}
              </Descriptions.Item>
              <Descriptions.Item label="价格">
                <Text strong style={{ color: '#f50', fontSize: 16 }}>
                  ¥{package_.price?.toLocaleString()}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="活动时长">
                {package_.durationHours} 小时
              </Descriptions.Item>
              <Descriptions.Item label="活动地点">
                {package_.location}
              </Descriptions.Item>
              <Descriptions.Item label="参与人数">
                {package_.minParticipants} - {package_.maxParticipants} 人
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(package_.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(package_.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title="详细描述" className="mb-16">
            <Paragraph>{package_.description}</Paragraph>
          </Card>

          {package_.culturalSignificance && (
            <Card title="文化意义" className="mb-16">
              <Paragraph>{package_.culturalSignificance}</Paragraph>
            </Card>
          )}

          <Row gutter={16}>
            {package_.highlights && package_.highlights.length > 0 && (
              <Col span={8}>
                <Card title="套餐亮点" size="small">
                  <List
                    size="small"
                    dataSource={package_.highlights}
                    renderItem={(item) => (
                      <List.Item>
                        <Text>• {item}</Text>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            )}

            {package_.includes && package_.includes.length > 0 && (
              <Col span={8}>
                <Card title="包含项目" size="small">
                  <List
                    size="small"
                    dataSource={package_.includes}
                    renderItem={(item) => (
                      <List.Item>
                        <Text style={{ color: '#52c41a' }}>✓ {item}</Text>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            )}

            {package_.excludes && package_.excludes.length > 0 && (
              <Col span={8}>
                <Card title="不包含项目" size="small">
                  <List
                    size="small"
                    dataSource={package_.excludes}
                    renderItem={(item) => (
                      <List.Item>
                        <Text style={{ color: '#ff4d4f' }}>✗ {item}</Text>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            )}
          </Row>
        </Col>

        <Col span={8}>
          {package_.images && package_.images.length > 0 && (
            <Card title="套餐图片" className="mb-16">
              <Image.PreviewGroup>
                {package_.images.map((image, index) => (
                  <Image
                    key={index}
                    width="100%"
                    src={image}
                    style={{ marginBottom: 8 }}
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />
                ))}
              </Image.PreviewGroup>
            </Card>
          )}

          <Card title="其他信息" size="small">
            <Descriptions column={1} size="small">
              {package_.bestSeason && (
                <Descriptions.Item label="最佳季节">
                  {package_.bestSeason}
                </Descriptions.Item>
              )}
              {package_.languageRequirements && (
                <Descriptions.Item label="语言要求">
                  {package_.languageRequirements}
                </Descriptions.Item>
              )}
              {package_.physicalRequirements && (
                <Descriptions.Item label="身体要求">
                  {package_.physicalRequirements}
                </Descriptions.Item>
              )}
              {package_.bookingNotice && (
                <Descriptions.Item label="预订须知">
                  {package_.bookingNotice}
                </Descriptions.Item>
              )}
              {package_.cancellationPolicy && (
                <Descriptions.Item label="取消政策">
                  {package_.cancellationPolicy}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PackageDetail;
