// 酒店列表页面

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Button,
  Space,
  Tag,
  Rate,
  Avatar,
  Progress,
  Dropdown,
  Modal,

  Row,
  Col,
  Select,
  Input
} from 'antd';
import {
  PlusOutlined,
  HomeOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  MoreOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import AdminTable from '@/components/AdminTable';
import { useTable } from '@/hooks/useTable';
import { adminApi } from '@/services/admin';
import { Hotel } from '@/types/admin';
import { notify } from '@/utils/notification';
import './index.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const HotelList: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<Record<string, any>>({});

  // 使用自定义表格Hook
  const {
    data: hotels,
    loading,
    pagination,
    selectedRowKeys,
    setSelectedRowKeys,
    handleTableChange,
    refresh,
    search,
  } = useTable<Hotel>({
    apiCall: adminApi.getHotels,
    defaultPageSize: 20,
  });

  // 表格列定义
  const columns: ColumnsType<Hotel> = [
    {
      title: '酒店信息',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (text, record) => (
        <div className="name-with-avatar">
          <Avatar
            size={48}
            src={record.images?.[0]}
            icon={<HomeOutlined />}
            shape="square"
          />
          <div className="name-info">
            <div className="name-primary">{text}</div>
            <div className="name-secondary">{record.address}</div>
          </div>
        </div>
      ),
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
      width: 100,
      filters: [
        { text: '康定', value: '康定' },
        { text: '稻城', value: '稻城' },
        { text: '色达', value: '色达' },
        { text: '理塘', value: '理塘' },
      ],
    },
    {
      title: '星级',
      dataIndex: 'starRating',
      key: 'starRating',
      width: 120,
      render: (rating) => (
        <div className="rating-display">
          <Rate disabled defaultValue={rating} style={{ fontSize: 14 }} />
          <span className="rating-text">({rating}星)</span>
        </div>
      ),
      sorter: true,
    },
    {
      title: '房间数',
      dataIndex: 'totalRooms',
      key: 'totalRooms',
      width: 100,
      render: (total, record) => (
        <div>
          <div className="number-display">{total}</div>
          <div className="name-secondary">可用: {record.availableRooms || 0}</div>
        </div>
      ),
      sorter: true,
    },
    {
      title: '入住率',
      dataIndex: 'occupancyRate',
      key: 'occupancyRate',
      width: 120,
      render: (rate) => {
        const percentage = Math.round((rate || 0) * 100);
        return (
          <Progress
            percent={percentage}
            size="small"
            status={percentage > 80 ? 'success' : percentage > 50 ? 'normal' : 'exception'}
            format={() => `${percentage}%`}
          />
        );
      },
      sorter: true,
    },
    {
      title: '评分',
      dataIndex: 'avgRating',
      key: 'avgRating',
      width: 100,
      render: (rating) => (
        <div className="rating-display">
          <span className="number-display">{rating?.toFixed(1) || '0.0'}</span>
          <span className="rating-text">分</span>
        </div>
      ),
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'error'}>
          {isActive ? '正常营业' : '暂停营业'}
        </Tag>
      ),
      filters: [
        { text: '正常营业', value: true },
        { text: '暂停营业', value: false },
      ],
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'view',
                icon: <EyeOutlined />,
                label: '查看详情',
                onClick: () => handleView(record.id),
              },
              {
                key: 'edit',
                icon: <EditOutlined />,
                label: '编辑',
                onClick: () => handleEdit(record.id),
              },
              {
                type: 'divider',
              },
              {
                key: 'delete',
                icon: <DeleteOutlined />,
                label: '删除',
                danger: true,
                onClick: () => handleDelete(record.id),
              },
            ],
          }}
          trigger={['click']}
        >
          <Button type="text" icon={<MoreOutlined />} />
        </Dropdown>
      ),
    },
  ];

  // 处理查看详情
  const handleView = (id: number) => {
    navigate(`/admin/hotels/view/${id}`);
  };

  // 处理编辑
  const handleEdit = (id: number) => {
    navigate(`/admin/hotels/edit/${id}`);
  };

  // 处理删除
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>确定要删除这个酒店吗？</p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
            注意：如果酒店有关联的预订记录，将无法删除。此操作会将酒店设置为停用状态。
          </p>
        </div>
      ),
      okText: '确定删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await adminApi.deleteHotel(id);
          notify.success('酒店删除成功');
          refresh();
        } catch (error: any) {
          console.error('Delete hotel failed:', error);
          if (error.message && error.message.includes('预订记录')) {
            notify.error('删除失败', '该酒店有关联的预订记录，无法删除');
          } else {
            notify.error('删除失败', error.message || '删除操作失败');
          }
        }
      },
    });
  };

  // 处理批量删除
  const handleBatchDelete = (selectedKeys: React.Key[]) => {
    Modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedKeys.length} 个酒店吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          // 这里调用批量删除API
          await adminApi.batchOperateHotels({
            action: 'delete',
            hotelIds: selectedKeys as number[],
          });
          notify.success('批量删除成功');
          setSelectedRowKeys([]);
          refresh();
        } catch (error) {
          notify.error('批量删除失败');
        }
      },
    });
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    search({ search: value });
  };

  // 处理筛选
  const handleFilter = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    search({ ...filters, [key]: value });
  };

  // 筛选器组件
  const renderFilters = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Select
          placeholder="选择城市"
          allowClear
          style={{ width: '100%' }}
          onChange={(value) => handleFilter('city', value)}
        >
          <Option value="康定">康定</Option>
          <Option value="稻城">稻城</Option>
          <Option value="色达">色达</Option>
          <Option value="理塘">理塘</Option>
        </Select>
      </Col>
      <Col span={6}>
        <Select
          placeholder="选择星级"
          allowClear
          style={{ width: '100%' }}
          onChange={(value) => handleFilter('starRating', value)}
        >
          <Option value={5}>五星级</Option>
          <Option value={4}>四星级</Option>
          <Option value={3}>三星级</Option>
          <Option value={2}>二星级</Option>
          <Option value={1}>一星级</Option>
        </Select>
      </Col>
      <Col span={6}>
        <Select
          placeholder="营业状态"
          allowClear
          style={{ width: '100%' }}
          onChange={(value) => handleFilter('isActive', value)}
        >
          <Option value={true}>正常营业</Option>
          <Option value={false}>暂停营业</Option>
        </Select>
      </Col>
    </Row>
  );

  return (
    <div className="hotel-list fade-in">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="flex-between">
          <div>
            <Title level={2} className="page-header-title">
              <HomeOutlined style={{ marginRight: 8 }} />
              酒店管理
            </Title>
            <Text className="page-header-subtitle">
              管理系统中的所有酒店信息，共 {pagination.total} 家酒店
            </Text>
          </div>
        </div>
      </div>

      {/* 表格 */}
      <AdminTable
        columns={columns}
        dataSource={hotels}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="id"
        selectedRowKeys={selectedRowKeys}
        onSelectChange={setSelectedRowKeys}
        showSearch
        searchPlaceholder="搜索酒店名称、地址"
        onSearch={handleSearch}
        showFilters
        filters={renderFilters()}
        actions={{
          refresh: {
            onClick: refresh,
          },
          batchDelete: {
            text: '批量删除',
            onClick: handleBatchDelete,
          },
        }}
      />
    </div>
  );
};

export default HotelList;
