import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  message,
  Tooltip,
  Typography,
  Row,
  Col,
  Statistic,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import {
  PackageBooking,
  BookingSearchParams,
  BookingStatistics,
  getStatusConfig,
  getPaymentStatusConfig,
  formatAmount,
  formatDateTime,
  formatDate,
} from '@/services/packageBookingApi';
import { mockPackageBookingApi } from '@/services/mockPackageBookingApi';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const PackageBookingList: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [bookings, setBookings] = useState<PackageBooking[]>([]);
  const [total, setTotal] = useState(0);
  const [statistics, setStatistics] = useState<BookingStatistics | null>(null);
  const [searchParams, setSearchParams] = useState<BookingSearchParams>({
    page: 0,
    size: 20,
  });

  // 模态框状态
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<PackageBooking | null>(null);

  // 加载预订列表
  const loadBookings = async (params: BookingSearchParams = searchParams) => {
    try {
      setLoading(true);
      const response = await mockPackageBookingApi.searchBookings(params);
      setBookings(response.data.content || []);
      setTotal(response.data.totalElements || 0);
    } catch (error) {
      console.error('加载预订列表失败:', error);
      message.error('加载预订列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await mockPackageBookingApi.getBookingStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  useEffect(() => {
    loadBookings();
    loadStatistics();
  }, []);

  // 搜索处理
  const handleSearch = (values: any) => {
    const params: BookingSearchParams = {
      ...searchParams,
      page: 0,
      ...values,
    };

    // 处理日期范围
    if (values.dateRange && values.dateRange.length === 2) {
      params.startDate = values.dateRange[0].format('YYYY-MM-DD');
      params.endDate = values.dateRange[1].format('YYYY-MM-DD');
    }
    delete params.dateRange;

    setSearchParams(params);
    loadBookings(params);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    const params: BookingSearchParams = {
      page: 0,
      size: 20,
    };
    setSearchParams(params);
    loadBookings(params);
  };

  // 分页处理
  const handleTableChange = (pagination: any) => {
    const params = {
      ...searchParams,
      page: pagination.current - 1,
      size: pagination.pageSize,
    };
    setSearchParams(params);
    loadBookings(params);
  };

  // 查看详情
  const handleViewDetail = (booking: PackageBooking) => {
    navigate(`/admin/package-bookings/detail/${booking.id}`);
  };

  // 确认预订
  const handleConfirm = (booking: PackageBooking) => {
    setSelectedBooking(booking);
    setConfirmModalVisible(true);
  };

  // 拒绝预订
  const handleReject = (booking: PackageBooking) => {
    setSelectedBooking(booking);
    setRejectModalVisible(true);
  };

  // 确认预订提交
  const handleConfirmSubmit = async (values: any) => {
    if (!selectedBooking) return;

    try {
      await mockPackageBookingApi.confirmBooking(selectedBooking.id, {
        remarks: values.remarks,
      });
      message.success('预订确认成功');
      setConfirmModalVisible(false);
      setSelectedBooking(null);
      loadBookings();
      loadStatistics();
    } catch (error) {
      console.error('确认预订失败:', error);
      message.error('确认预订失败');
    }
  };

  // 拒绝预订提交
  const handleRejectSubmit = async (values: any) => {
    if (!selectedBooking) return;

    try {
      await mockPackageBookingApi.rejectBooking(selectedBooking.id, {
        reason: values.reason,
      });
      message.success('预订拒绝成功');
      setRejectModalVisible(false);
      setSelectedBooking(null);
      loadBookings();
      loadStatistics();
    } catch (error) {
      console.error('拒绝预订失败:', error);
      message.error('拒绝预订失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '预订号',
      dataIndex: 'bookingNumber',
      key: 'bookingNumber',
      width: 160,
      render: (text: string) => (
        <Text copyable={{ text }} style={{ fontFamily: 'monospace' }}>
          {text}
        </Text>
      ),
    },
    {
      title: '套餐信息',
      key: 'packageInfo',
      width: 200,
      render: (record: PackageBooking) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.packageName}</div>
          {record.packageLocation && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.packageLocation}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: '联系人',
      key: 'contact',
      width: 120,
      render: (record: PackageBooking) => (
        <div>
          <div>{record.contactName}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.contactPhone}
          </Text>
        </div>
      ),
    },
    {
      title: '预订日期',
      dataIndex: 'bookingDate',
      key: 'bookingDate',
      width: 100,
      render: (date: string) => formatDate(date),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      align: 'right' as const,
      render: (amount: number) => (
        <Text strong style={{ color: '#f50' }}>
          {formatAmount(amount)}
        </Text>
      ),
    },
    {
      title: '预订状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const config = getStatusConfig(status);
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status: string) => {
        const config = getPaymentStatusConfig(status);
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (time: string) => formatDateTime(time),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (record: PackageBooking) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'PENDING' && (
            <>
              <Tooltip title="确认预订">
                <Button
                  type="text"
                  size="small"
                  icon={<CheckOutlined />}
                  style={{ color: '#52c41a' }}
                  onClick={() => handleConfirm(record)}
                />
              </Tooltip>
              <Tooltip title="拒绝预订">
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  danger
                  onClick={() => handleReject(record)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>文化套餐预订管理</Title>

      {/* 统计卡片 */}
      {statistics && (
        <>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总预订数"
                  value={statistics.totalBookings}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="待确认预订"
                  value={statistics.pendingBookings}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="今日新增"
                  value={statistics.todayBookings}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="已收入金额"
                  value={statistics.monthlyRevenue}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#f50' }}
                />
              </Card>
            </Col>
          </Row>

          {/* 收入详细统计 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总预订金额"
                  value={statistics.totalRevenue || 0}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#722ed1' }}
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  收入占比: {statistics.totalRevenue > 0 ? ((statistics.monthlyRevenue / statistics.totalRevenue) * 100).toFixed(1) : 0}%
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="待收金额"
                  value={statistics.pendingRevenue || 0}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#fa8c16' }}
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  占比: {statistics.totalRevenue > 0 ? (((statistics.pendingRevenue || 0) / statistics.totalRevenue) * 100).toFixed(1) : 0}%
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="已退款金额"
                  value={statistics.refundedRevenue || 0}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#ff4d4f' }}
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  占比: {statistics.totalRevenue > 0 ? (((statistics.refundedRevenue || 0) / statistics.totalRevenue) * 100).toFixed(1) : 0}%
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="完成订单收入"
                  value={statistics.revenueByStatus?.COMPLETED || 0}
                  precision={2}
                  prefix="¥"
                  valueStyle={{ color: '#52c41a' }}
                />
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  占比: {statistics.totalRevenue > 0 ? (((statistics.revenueByStatus?.COMPLETED || 0) / statistics.totalRevenue) * 100).toFixed(1) : 0}%
                </div>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="keyword">
            <Input
              placeholder="搜索预订号、联系人、套餐名称"
              style={{ width: 250 }}
              allowClear
            />
          </Form.Item>
          <Form.Item name="status">
            <Select placeholder="预订状态" style={{ width: 120 }} allowClear>
              <Select.Option value="PENDING">待确认</Select.Option>
              <Select.Option value="CONFIRMED">已确认</Select.Option>
              <Select.Option value="IN_PROGRESS">进行中</Select.Option>
              <Select.Option value="COMPLETED">已完成</Select.Option>
              <Select.Option value="CANCELLED">已取消</Select.Option>
              <Select.Option value="NO_SHOW">未到场</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="paymentStatus">
            <Select placeholder="支付状态" style={{ width: 120 }} allowClear>
              <Select.Option value="PENDING">待支付</Select.Option>
              <Select.Option value="PARTIAL">部分支付</Select.Option>
              <Select.Option value="PAID">已支付</Select.Option>
              <Select.Option value="REFUNDED">已退款</Select.Option>
              <Select.Option value="FAILED">支付失败</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button icon={<ReloadOutlined />} onClick={() => loadBookings()}>
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 预订列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={bookings}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: searchParams.page! + 1,
            pageSize: searchParams.size,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 确认预订模态框 */}
      <Modal
        title="确认预订"
        open={confirmModalVisible}
        onCancel={() => {
          setConfirmModalVisible(false);
          setSelectedBooking(null);
        }}
        footer={null}
      >
        {selectedBooking && (
          <Form onFinish={handleConfirmSubmit} layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <Text strong>预订信息：</Text>
              <div>预订号：{selectedBooking.bookingNumber}</div>
              <div>套餐名称：{selectedBooking.packageName}</div>
              <div>联系人：{selectedBooking.contactName}</div>
              <div>联系电话：{selectedBooking.contactPhone}</div>
            </div>
            <Form.Item name="remarks" label="备注信息">
              <TextArea
                rows={3}
                placeholder="可添加确认备注信息（可选）"
                maxLength={500}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button
                  onClick={() => {
                    setConfirmModalVisible(false);
                    setSelectedBooking(null);
                  }}
                >
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  确认预订
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 拒绝预订模态框 */}
      <Modal
        title="拒绝预订"
        open={rejectModalVisible}
        onCancel={() => {
          setRejectModalVisible(false);
          setSelectedBooking(null);
        }}
        footer={null}
      >
        {selectedBooking && (
          <Form onFinish={handleRejectSubmit} layout="vertical">
            <div style={{ marginBottom: 16 }}>
              <Text strong>预订信息：</Text>
              <div>预订号：{selectedBooking.bookingNumber}</div>
              <div>套餐名称：{selectedBooking.packageName}</div>
              <div>联系人：{selectedBooking.contactName}</div>
              <div>联系电话：{selectedBooking.contactPhone}</div>
            </div>
            <Form.Item
              name="reason"
              label="拒绝原因"
              rules={[{ required: true, message: '请输入拒绝原因' }]}
            >
              <TextArea
                rows={3}
                placeholder="请输入拒绝预订的原因"
                maxLength={500}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button
                  onClick={() => {
                    setRejectModalVisible(false);
                    setSelectedBooking(null);
                  }}
                >
                  取消
                </Button>
                <Button type="primary" danger htmlType="submit">
                  拒绝预订
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default PackageBookingList;
