import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Typography,
  Divider,
  Timeline,
  Row,
  Col,
  Spin,
} from 'antd';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import {
  PackageBooking,
  getStatusConfig,
  getPaymentStatusConfig,
  formatAmount,
  formatDateTime,
  formatDate,
} from '@/services/packageBookingApi';
import { mockPackageBookingApi } from '@/services/mockPackageBookingApi';

const { Title, Text } = Typography;
const { TextArea } = Input;

const PackageBookingDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [booking, setBooking] = useState<PackageBooking | null>(null);

  // 模态框状态
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [cancelModalVisible, setCancelModalVisible] = useState(false);

  // 加载预订详情
  const loadBookingDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await mockPackageBookingApi.getBookingById(parseInt(id));
      setBooking(response.data);
    } catch (error) {
      console.error('加载预订详情失败:', error);
      message.error('加载预订详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBookingDetail();
  }, [id]);

  // 确认预订
  const handleConfirmSubmit = async (values: any) => {
    if (!booking) return;

    try {
      await mockPackageBookingApi.confirmBooking(booking.id, {
        remarks: values.remarks,
      });
      message.success('预订确认成功');
      setConfirmModalVisible(false);
      loadBookingDetail();
    } catch (error) {
      console.error('确认预订失败:', error);
      message.error('确认预订失败');
    }
  };

  // 拒绝预订
  const handleRejectSubmit = async (values: any) => {
    if (!booking) return;

    try {
      await mockPackageBookingApi.rejectBooking(booking.id, {
        reason: values.reason,
      });
      message.success('预订拒绝成功');
      setRejectModalVisible(false);
      loadBookingDetail();
    } catch (error) {
      console.error('拒绝预订失败:', error);
      message.error('拒绝预订失败');
    }
  };

  // 取消预订
  const handleCancelSubmit = async (values: any) => {
    if (!booking) return;

    try {
      await mockPackageBookingApi.cancelBooking(booking.id, {
        reason: values.reason,
      });
      message.success('预订取消成功');
      setCancelModalVisible(false);
      loadBookingDetail();
    } catch (error) {
      console.error('取消预订失败:', error);
      message.error('取消预订失败');
    }
  };

  // 完成预订
  const handleComplete = async () => {
    if (!booking) return;

    Modal.confirm({
      title: '确认完成预订',
      content: '确定要将此预订标记为已完成吗？',
      onOk: async () => {
        try {
          await mockPackageBookingApi.completeBooking(booking.id);
          message.success('预订完成成功');
          loadBookingDetail();
        } catch (error) {
          console.error('完成预订失败:', error);
          message.error('完成预订失败');
        }
      },
    });
  };

  // 生成时间线数据
  const getTimelineItems = () => {
    if (!booking) return [];

    const items = [
      {
        color: 'blue',
        children: (
          <div>
            <div>预订创建</div>
            <Text type="secondary">{formatDateTime(booking.createdAt)}</Text>
          </div>
        ),
      },
    ];

    if (booking.confirmedAt) {
      items.push({
        color: 'green',
        children: (
          <div>
            <div>预订确认</div>
            <Text type="secondary">{formatDateTime(booking.confirmedAt)}</Text>
          </div>
        ),
      });
    }

    if (booking.cancelledAt) {
      items.push({
        color: 'red',
        children: (
          <div>
            <div>预订取消</div>
            <Text type="secondary">{formatDateTime(booking.cancelledAt)}</Text>
            {booking.cancellationReason && (
              <div>
                <Text type="secondary">原因：{booking.cancellationReason}</Text>
              </div>
            )}
          </div>
        ),
      });
    }

    if (booking.status === 'COMPLETED') {
      items.push({
        color: 'green',
        children: (
          <div>
            <div>预订完成</div>
            <Text type="secondary">{formatDateTime(booking.updatedAt)}</Text>
          </div>
        ),
      });
    }

    return items;
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!booking) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Text>预订不存在</Text>
      </div>
    );
  }

  const statusConfig = getStatusConfig(booking.status);
  const paymentStatusConfig = getPaymentStatusConfig(booking.paymentStatus);

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/admin/package-bookings')}
          >
            返回列表
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            预订详情 - {booking.bookingNumber}
          </Title>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          {/* 基本信息 */}
          <Card title="基本信息" style={{ marginBottom: 24 }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="预订号" span={2}>
                <Text copyable style={{ fontFamily: 'monospace' }}>
                  {booking.bookingNumber}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="套餐名称">
                {booking.packageName}
              </Descriptions.Item>
              <Descriptions.Item label="套餐地点">
                {booking.packageLocation || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预订日期">
                {formatDate(booking.bookingDate)}
              </Descriptions.Item>
              <Descriptions.Item label="预订时间">
                {booking.bookingTime || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="参与人数">
                {booking.participantCount} 人
              </Descriptions.Item>
              <Descriptions.Item label="单价">
                {formatAmount(booking.unitPrice)}
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                <Text strong style={{ color: '#f50', fontSize: '16px' }}>
                  {formatAmount(booking.totalAmount)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="预订状态">
                <Tag color={statusConfig.color}>{statusConfig.text}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="支付状态">
                <Tag color={paymentStatusConfig.color}>
                  {paymentStatusConfig.text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {formatDateTime(booking.createdAt)}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 联系信息 */}
          <Card title="联系信息" style={{ marginBottom: 24 }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="联系人姓名">
                {booking.contactName}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                <Text copyable>{booking.contactPhone}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="联系邮箱" span={2}>
                {booking.contactEmail ? (
                  <Text copyable>{booking.contactEmail}</Text>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 详细信息 */}
          <Card title="详细信息">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="参与者信息">
                {booking.participantInfo ? (
                  <div style={{ whiteSpace: 'pre-wrap' }}>
                    {booking.participantInfo}
                  </div>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              <Descriptions.Item label="特殊要求">
                {booking.specialRequirements ? (
                  <div style={{ whiteSpace: 'pre-wrap' }}>
                    {booking.specialRequirements}
                  </div>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              {booking.cancellationReason && (
                <Descriptions.Item label="取消原因">
                  <div style={{ whiteSpace: 'pre-wrap' }}>
                    {booking.cancellationReason}
                  </div>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>

        <Col span={8}>
          {/* 操作按钮 */}
          <Card title="操作" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {booking.status === 'PENDING' && (
                <>
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    block
                    onClick={() => setConfirmModalVisible(true)}
                  >
                    确认预订
                  </Button>
                  <Button
                    danger
                    icon={<CloseOutlined />}
                    block
                    onClick={() => setRejectModalVisible(true)}
                  >
                    拒绝预订
                  </Button>
                </>
              )}
              {(booking.status === 'CONFIRMED' ||
                booking.status === 'IN_PROGRESS') && (
                <>
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    block
                    onClick={handleComplete}
                  >
                    完成预订
                  </Button>
                  <Button
                    danger
                    icon={<CloseOutlined />}
                    block
                    onClick={() => setCancelModalVisible(true)}
                  >
                    取消预订
                  </Button>
                </>
              )}
            </Space>
          </Card>

          {/* 预订时间线 */}
          <Card title="预订历史">
            <Timeline items={getTimelineItems()} />
          </Card>
        </Col>
      </Row>

      {/* 确认预订模态框 */}
      <Modal
        title="确认预订"
        open={confirmModalVisible}
        onCancel={() => setConfirmModalVisible(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleConfirmSubmit} layout="vertical">
          <Form.Item name="remarks" label="备注信息">
            <TextArea
              rows={3}
              placeholder="可添加确认备注信息（可选）"
              maxLength={500}
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setConfirmModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确认预订
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 拒绝预订模态框 */}
      <Modal
        title="拒绝预订"
        open={rejectModalVisible}
        onCancel={() => setRejectModalVisible(false)}
        footer={null}
      >
        <Form onFinish={handleRejectSubmit} layout="vertical">
          <Form.Item
            name="reason"
            label="拒绝原因"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入拒绝预订的原因"
              maxLength={500}
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setRejectModalVisible(false)}>取消</Button>
              <Button type="primary" danger htmlType="submit">
                拒绝预订
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 取消预订模态框 */}
      <Modal
        title="取消预订"
        open={cancelModalVisible}
        onCancel={() => setCancelModalVisible(false)}
        footer={null}
      >
        <Form onFinish={handleCancelSubmit} layout="vertical">
          <Form.Item name="reason" label="取消原因">
            <TextArea
              rows={3}
              placeholder="请输入取消预订的原因（可选）"
              maxLength={500}
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setCancelModalVisible(false)}>取消</Button>
              <Button type="primary" danger htmlType="submit">
                确认取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PackageBookingDetail;
