import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Upload,
  message,
  Popconfirm,
  Image,
  Tooltip,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  DragOutlined,
  PoweroffOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { bannerService } from '@/services/banner';
import ImageUpload from '@/components/ImageUpload';

interface Banner {
  id: number;
  title: string;
  description?: string;
  imageUrl: string;
  linkUrl?: string;
  displayOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const BannerList: React.FC = () => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 加载轮播图列表
  const loadBanners = async (page = 1, size = 10) => {
    setLoading(true);
    try {
      const response = await bannerService.getAllBanners(page - 1, size);
      if (response.success) {
        setBanners(response.data.content);
        setPagination({
          current: page,
          pageSize: size,
          total: response.data.totalElements,
        });
      } else {
        message.error(response.message || '获取轮播图列表失败');
      }
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      message.error('获取轮播图列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    loadBanners();
  }, []);

  // 处理表格分页变化
  const handleTableChange = (pagination: any) => {
    loadBanners(pagination.current, pagination.pageSize);
  };

  // 打开新增/编辑模态框
  const openModal = (banner?: Banner) => {
    setEditingBanner(banner || null);
    setModalVisible(true);
    
    if (banner) {
      form.setFieldsValue({
        title: banner.title,
        description: banner.description,
        imageUrl: banner.imageUrl,
        linkUrl: banner.linkUrl,
        displayOrder: banner.displayOrder,
        isActive: banner.isActive,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        displayOrder: banners.length + 1,
        isActive: true,
      });
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingBanner(null);
    form.resetFields();
  };

  // 保存轮播图
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingBanner) {
        // 更新轮播图
        const response = await bannerService.updateBanner(editingBanner.id, values);
        if (response.success) {
          message.success('轮播图更新成功');
          closeModal();
          loadBanners(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || '更新轮播图失败');
        }
      } else {
        // 创建新轮播图
        const response = await bannerService.createBanner(values);
        if (response.success) {
          message.success('轮播图创建成功');
          closeModal();
          loadBanners(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || '创建轮播图失败');
        }
      }
    } catch (error) {
      console.error('保存轮播图失败:', error);
      message.error('保存轮播图失败');
    }
  };

  // 删除轮播图
  const handleDelete = async (id: number) => {
    try {
      const response = await bannerService.deleteBanner(id);
      if (response.success) {
        message.success('轮播图删除成功');
        loadBanners(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '删除轮播图失败');
      }
    } catch (error) {
      console.error('删除轮播图失败:', error);
      message.error('删除轮播图失败');
    }
  };

  // 切换轮播图状态
  const handleToggleStatus = async (id: number) => {
    try {
      const response = await bannerService.toggleBannerStatus(id);
      if (response.success) {
        message.success('轮播图状态切换成功');
        loadBanners(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '切换轮播图状态失败');
      }
    } catch (error) {
      console.error('切换轮播图状态失败:', error);
      message.error('切换轮播图状态失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<Banner> = [
    {
      title: '排序',
      dataIndex: 'displayOrder',
      key: 'displayOrder',
      width: 80,
      sorter: (a, b) => a.displayOrder - b.displayOrder,
      render: (order) => (
        <Tag color="blue">
          <DragOutlined style={{ marginRight: 4 }} />
          {order}
        </Tag>
      ),
    },
    {
      title: '预览',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 120,
      render: (imageUrl) => (
        <Image
          width={80}
          height={45}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '链接',
      dataIndex: 'linkUrl',
      key: 'linkUrl',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '禁用' : '启用'}>
            <Button
              type="text"
              icon={<PoweroffOutlined />}
              onClick={() => handleToggleStatus(record.id)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个轮播图吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="轮播图管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            新增轮播图
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={banners}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingBanner ? '编辑轮播图' : '新增轮播图'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={closeModal}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isActive: true,
            displayOrder: 1,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="标题"
                rules={[
                  { required: true, message: '请输入轮播图标题' },
                  { max: 200, message: '标题长度不能超过200个字符' },
                ]}
              >
                <Input placeholder="请输入轮播图标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="displayOrder"
                label="显示顺序"
                rules={[{ required: true, message: '请输入显示顺序' }]}
              >
                <InputNumber
                  min={1}
                  placeholder="请输入显示顺序"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ max: 500, message: '描述长度不能超过500个字符' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入轮播图描述"
            />
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="轮播图图片"
            rules={[
              { required: true, message: '请上传轮播图图片' },
            ]}
            extra="支持上传 JPG、PNG、GIF、WebP 格式图片，单张图片不超过 5MB，建议尺寸 1920x800"
          >
            <ImageUpload
              uploadType="banner"
              maxCount={1}
              listType="picture-card"
              onChange={(urls) => {
                // 由于轮播图只支持单张图片，取第一个URL
                const imageUrl = urls.length > 0 ? urls[0] : '';
                form.setFieldsValue({ imageUrl });
              }}
              value={(() => {
                const imageUrl = form.getFieldValue('imageUrl');
                if (typeof imageUrl === 'string' && imageUrl) {
                  return [imageUrl];
                } else if (Array.isArray(imageUrl)) {
                  return imageUrl;
                }
                return [];
              })()}
            />
          </Form.Item>

          <Form.Item
            name="linkUrl"
            label="链接地址"
            rules={[{ max: 500, message: 'URL长度不能超过500个字符' }]}
          >
            <Input placeholder="请输入点击跳转的链接地址（可选）" />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="状态"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BannerList;
