// 管理员后台布局组件

import React, { useState } from 'react';
import { Layout, Menu, Avatar, Dropdown, Space, Typography, Button, Badge } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  HomeOutlined,
  BankOutlined,
  CalendarOutlined,
  UserOutlined,
  StarOutlined,
  GiftOutlined,
  DollarOutlined,
  BarChartOutlined,
  DatabaseOutlined,
  SettingOutlined,
  PictureOutlined,
  LogoutOutlined,
  BellOutlined,
  UserSwitchOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { MenuItem } from '@/types/common';

import FeedbackEnhancer from '@/components/FeedbackEnhancer';
import './index.css';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const AdminLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  // 菜单配置
  const menuItems: MenuItem[] = [
    {
      key: '/admin/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      path: '/admin/dashboard',
    },
    {
      key: '/admin/hotels',
      icon: <HomeOutlined />,
      label: '酒店管理',
      children: [
        { key: '/admin/hotels/list', label: '酒店列表', path: '/admin/hotels/list' },
      ],
    },
    {
      key: '/admin/rooms',
      icon: <BankOutlined />,
      label: '房间管理',
      children: [
        { key: '/admin/rooms/list', label: '房间列表', path: '/admin/rooms' },
        { key: '/admin/rooms/schedule', label: '房间调度', path: '/admin/rooms/schedule' },
      ],
    },
    {
      key: '/admin/bookings',
      icon: <CalendarOutlined />,
      label: '预订管理',
      path: '/admin/bookings',
    },
    {
      key: '/admin/users',
      icon: <UserOutlined />,
      label: '用户管理',
      children: [
        { key: '/admin/users/list', label: '用户列表', path: '/admin/users' },
      ],
    },
    {
      key: '/admin/reviews',
      icon: <StarOutlined />,
      label: '评价管理',
      children: [
        { key: '/admin/reviews/list', label: '评价列表', path: '/admin/reviews/list' },
        { key: '/admin/reviews/pending', label: '待审核评价', path: '/admin/reviews/pending' },
      ],
    },
    {
      key: '/admin/packages',
      icon: <GiftOutlined />,
      label: '文化套餐',
      children: [
        { key: '/admin/packages/list', label: '套餐管理', path: '/admin/packages' },
        { key: '/admin/package-bookings/list', label: '预订管理', path: '/admin/package-bookings' },
      ],
    },
    {
      key: '/admin/banners',
      icon: <PictureOutlined />,
      label: '轮播图管理',
      path: '/admin/banners',
    },
    {
      key: '/admin/reports',
      icon: <BarChartOutlined />,
      label: '数据报表',
      path: '/admin/reports',
    },

    {
      key: '/admin/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      path: '/admin/settings',
    },
  ];

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserSwitchOutlined />,
      label: '个人设置',
      onClick: () => navigate('/admin/profile'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;

    // 精确匹配
    if (pathname === '/admin/dashboard') return ['/admin/dashboard'];
    if (pathname.startsWith('/admin/hotels')) return ['/admin/hotels/list'];
    if (pathname.startsWith('/admin/rooms')) {
      if (pathname.includes('schedule')) return ['/admin/rooms/schedule'];
      return ['/admin/rooms/list'];
    }
    if (pathname.startsWith('/admin/bookings')) {
      return ['/admin/bookings'];
    }
    if (pathname.startsWith('/admin/users')) {
      return ['/admin/users/list'];
    }
    if (pathname.startsWith('/admin/reviews')) {
      if (pathname.includes('pending')) return ['/admin/reviews/pending'];
      return ['/admin/reviews/list'];
    }
    if (pathname.startsWith('/admin/packages')) return ['/admin/packages'];
    if (pathname.startsWith('/admin/settings')) return ['/admin/settings'];

    return [pathname];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const pathname = location.pathname;
    const openKeys: string[] = [];
    
    if (pathname.startsWith('/admin/hotels')) openKeys.push('/admin/hotels');
    if (pathname.startsWith('/admin/reviews')) openKeys.push('/admin/reviews');
    
    return openKeys;
  };

  return (
    <>
      <Layout className="admin-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={256}
        className="admin-sider"
        theme="light"
      >
        <div className="admin-logo">
          <img 
            src="/logo.png" 
            alt="甘孜酒店管理" 
            className="admin-logo-img"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
          {!collapsed && (
            <div className="admin-logo-text">
              <div className="admin-logo-title">甘孜酒店管理</div>
              <div className="admin-logo-subtitle">管理后台</div>
            </div>
          )}
        </div>

        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          className="admin-menu"
        />
      </Sider>

      <Layout className="admin-main">
        <Header className="admin-header">
          <div className="admin-header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="admin-trigger"
            />
          </div>

          <div className="admin-header-right">
            <Space size="middle">
              <Badge count={5} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  className="admin-header-btn"
                />
              </Badge>

              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <div className="admin-user-info">
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    src={user?.avatarUrl}
                    className="admin-avatar"
                  />
                  {!collapsed && (
                    <div className="admin-user-text">
                      <Text className="admin-username">{user?.fullName || user?.username}</Text>
                      <Text type="secondary" className="admin-role">管理员</Text>
                    </div>
                  )}
                </div>
              </Dropdown>
            </Space>
          </div>
        </Header>

        <Content className="admin-content">
          <div className="admin-content-wrapper">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>

      {/* 反馈组件 */}
      <FeedbackEnhancer />
    </>
  );
};

export default AdminLayout;
