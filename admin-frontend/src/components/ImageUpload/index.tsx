import React, { useState, useEffect } from 'react';
import { Upload, message, Modal } from 'antd';
import { PlusOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/es/upload';
import { adminApi } from '@/services/admin';
import './index.css';

interface ImageUploadProps {
  value?: string[];
  onChange?: (urls: string[]) => void;
  maxCount?: number;
  uploadType: 'hotel' | 'package' | 'room' | 'banner';
  relatedId?: number;
  disabled?: boolean;
  listType?: 'picture-card' | 'picture';
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = [],
  onChange,
  maxCount = 8,
  uploadType,
  relatedId,
  disabled = false,
  listType = 'picture-card',
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>(() => {
    const urls = Array.isArray(value) ? value : [];
    return urls.map((url, index) => ({
      uid: `${index}`,
      name: `image-${index}`,
      status: 'done' as const,
      url,
    }));
  });

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  // 当value属性变化时更新fileList
  useEffect(() => {
    const urls = Array.isArray(value) ? value : [];
    const currentUrls = fileList.map(file => file.url).filter(Boolean);

    // 只有当URLs实际发生变化时才更新fileList
    if (JSON.stringify(urls) !== JSON.stringify(currentUrls)) {
      const newFileList = urls.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}`,
        status: 'done' as const,
        url,
      }));
      setFileList(newFileList);
    }
  }, [value, fileList]);

  // 获取上传URL
  const getUploadUrl = () => {
    const baseUrl = '/api/upload';
    switch (uploadType) {
      case 'hotel':
        return `${baseUrl}/hotel-image`;
      case 'package':
        return `${baseUrl}/package-image`;
      case 'room':
        return `${baseUrl}/room-image`;
      case 'banner':
        return `${baseUrl}/banner-image`;
      default:
        return `${baseUrl}/hotel-image`;
    }
  };

  // 上传前检查
  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }
    return true;
  };

  // 处理上传变化
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);

    // 提取成功上传的图片URL
    const urls = newFileList
      .filter(file => file.status === 'done')
      .map(file => {
        // 处理后端返回的ApiResponse格式数据
        if (file.response?.data?.url) {
          return file.response.data.url;
        }
        return file.response?.url || file.url;
      })
      .filter(Boolean);

    onChange?.(urls);
  };

  // 处理预览
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  // 处理删除
  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter(item => item.uid !== file.uid);
    setFileList(newFileList);

    const urls = newFileList
      .filter(item => item.status === 'done')
      .map(item => item.response?.url || item.url)
      .filter(Boolean);

    onChange?.(urls);
  };

  // 自定义上传请求
  const customRequest = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    try {
      let uploadPromise;

      // 根据上传类型选择对应的API
      switch (uploadType) {
        case 'banner':
          uploadPromise = adminApi.uploadBannerImage(file);
          break;
        case 'hotel':
          uploadPromise = adminApi.uploadHotelImage(file, relatedId);
          break;
        case 'package':
          uploadPromise = adminApi.uploadPackageImage(file, relatedId);
          break;
        case 'room':
          uploadPromise = adminApi.uploadRoomImage(file, relatedId);
          break;
        default:
          uploadPromise = adminApi.uploadHotelImage(file, relatedId);
      }

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        const percent = Math.min(90, Math.random() * 80 + 10);
        onProgress({ percent });
      }, 100);

      const result = await uploadPromise;

      clearInterval(progressInterval);
      onProgress({ percent: 100 });

      onSuccess(result);
      message.success('图片上传成功');

    } catch (error: any) {
      console.error('Upload failed:', error);
      onError(error);
      message.error(error.message || '上传失败');
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传图片</div>
    </div>
  );

  return (
    <div className="image-upload">
      <Upload
        listType={listType}
        fileList={fileList}
        onChange={handleChange}
        onPreview={handlePreview}
        onRemove={handleRemove}
        beforeUpload={beforeUpload}
        customRequest={customRequest}
        multiple
        disabled={disabled}
        className="image-upload-list"
      >
        {fileList.length >= maxCount ? null : uploadButton}
      </Upload>
      
      <Modal
        visible={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        centered
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

// 获取base64预览
const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });

export default ImageUpload;
