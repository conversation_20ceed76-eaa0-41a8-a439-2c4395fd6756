<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘孜州酒店预订系统 - 增强数据演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-trend {
            color: #28a745;
            font-weight: 600;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .booking-trend-chart {
            height: 300px;
            display: flex;
            align-items: end;
            justify-content: space-around;
            padding: 20px 10px;
            border-bottom: 2px solid #eee;
            position: relative;
        }
        
        .trend-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            margin: 0 5px;
        }
        
        .bar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 40px;
            border-radius: 4px 4px 0 0;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .bar:hover {
            opacity: 0.8;
            transform: scale(1.05);
        }
        
        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            font-weight: bold;
            color: #667eea;
        }
        
        .bar-label {
            font-size: 11px;
            color: #666;
            text-align: center;
            transform: rotate(-45deg);
            margin-top: 5px;
        }
        
        .revenue-chart {
            height: 300px;
            display: flex;
            align-items: end;
            justify-content: center;
            gap: 20px;
            padding: 20px;
        }
        
        .revenue-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 120px;
        }
        
        .revenue-bar-fill {
            width: 60px;
            border-radius: 4px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .revenue-bar-fill:nth-child(1) { background: linear-gradient(45deg, #1890ff, #40a9ff); }
        .revenue-bar-fill:nth-child(2) { background: linear-gradient(45deg, #52c41a, #73d13d); }
        .revenue-bar-fill:nth-child(3) { background: linear-gradient(45deg, #faad14, #ffc53d); }
        .revenue-bar-fill:nth-child(4) { background: linear-gradient(45deg, #f5222d, #ff4d4f); }
        .revenue-bar-fill:nth-child(5) { background: linear-gradient(45deg, #722ed1, #9254de); }
        
        .revenue-bar-fill:hover {
            opacity: 0.8;
            transform: scale(1.05);
        }
        
        .revenue-label {
            font-size: 10px;
            text-align: center;
            color: #666;
            margin-bottom: 5px;
            word-wrap: break-word;
            line-height: 1.2;
        }
        
        .revenue-value {
            font-size: 12px;
            font-weight: bold;
            color: #28a745;
        }
        
        .pie-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            height: 300px;
        }
        
        .pie-chart {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .pie-center {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #333;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .pie-legend {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        
        .legend-text {
            font-size: 14px;
            color: #333;
        }
        
        .summary-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .summary-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .summary-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .summary-item p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .access-links {
            margin-top: 30px;
            text-align: center;
        }
        
        .access-links a {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .access-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ 甘孜州酒店预订系统</h1>
            <p>增强数据演示 - 管理后台报表功能</p>
        </div>

        <!-- 关键指标卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 总预订数</h3>
                <div class="stat-value">156</div>
                <div class="stat-trend">+18% ↗️</div>
            </div>
            <div class="stat-card">
                <h3>👥 总用户数</h3>
                <div class="stat-value">89</div>
                <div class="stat-trend">+12% ↗️</div>
            </div>
            <div class="stat-card">
                <h3>🏨 总酒店数</h3>
                <div class="stat-value">12</div>
                <div class="stat-trend">+9% ↗️</div>
            </div>
            <div class="stat-card">
                <h3>💰 本月收入</h3>
                <div class="stat-value">¥94,800</div>
                <div class="stat-trend">+25% ↗️</div>
            </div>
            <div class="stat-card">
                <h3>🏠 入住率</h3>
                <div class="stat-value">78.5%</div>
                <div class="stat-trend">健康水平</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <!-- 预订趋势图 -->
            <div class="chart-card">
                <div class="chart-title">📈 预订趋势（最近7天）</div>
                <div class="booking-trend-chart">
                    <div class="trend-bar">
                        <div class="bar" style="height: 150px;">
                            <div class="bar-value">15</div>
                        </div>
                        <div class="bar-label">08-31</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 120px;">
                            <div class="bar-value">12</div>
                        </div>
                        <div class="bar-label">09-01</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 80px;">
                            <div class="bar-value">8</div>
                        </div>
                        <div class="bar-label">09-02</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 100px;">
                            <div class="bar-value">10</div>
                        </div>
                        <div class="bar-label">09-03</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 90px;">
                            <div class="bar-value">9</div>
                        </div>
                        <div class="bar-label">09-04</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 110px;">
                            <div class="bar-value">11</div>
                        </div>
                        <div class="bar-label">09-05</div>
                    </div>
                    <div class="trend-bar">
                        <div class="bar" style="height: 140px;">
                            <div class="bar-value">14</div>
                        </div>
                        <div class="bar-label">09-06</div>
                    </div>
                </div>
            </div>

            <!-- 收入分布图 -->
            <div class="chart-card">
                <div class="chart-title">💰 酒店收入分布</div>
                <div class="revenue-chart">
                    <div class="revenue-bar">
                        <div class="revenue-bar-fill" style="height: 180px; background: linear-gradient(45deg, #1890ff, #40a9ff);"></div>
                        <div class="revenue-label">稻城亚丁香格里拉大酒店</div>
                        <div class="revenue-value" style="color: #1890ff;">¥28,440</div>
                    </div>
                    <div class="revenue-bar">
                        <div class="revenue-bar-fill" style="height: 150px; background: linear-gradient(45deg, #52c41a, #73d13d);"></div>
                        <div class="revenue-label">康定情歌大酒店</div>
                        <div class="revenue-value" style="color: #52c41a;">¥23,700</div>
                    </div>
                    <div class="revenue-bar">
                        <div class="revenue-bar-fill" style="height: 120px; background: linear-gradient(45deg, #faad14, #ffc53d);"></div>
                        <div class="revenue-label">丹巴美人谷度假村</div>
                        <div class="revenue-value" style="color: #faad14;">¥18,960</div>
                    </div>
                    <div class="revenue-bar">
                        <div class="revenue-bar-fill" style="height: 90px; background: linear-gradient(45deg, #f5222d, #ff4d4f);"></div>
                        <div class="revenue-label">泸定桥畔精品酒店</div>
                        <div class="revenue-value" style="color: #f5222d;">¥14,220</div>
                    </div>
                    <div class="revenue-bar">
                        <div class="revenue-bar-fill" style="height: 60px; background: linear-gradient(45deg, #722ed1, #9254de);"></div>
                        <div class="revenue-label">九龙山温泉酒店</div>
                        <div class="revenue-value" style="color: #722ed1;">¥9,480</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 收入占比饼图 -->
        <div class="chart-card">
            <div class="chart-title">🥧 收入占比分析</div>
            <div class="pie-section">
                <div class="pie-chart" style="background: conic-gradient(#1890ff 0% 30%, #52c41a 30% 55%, #faad14 55% 75%, #f5222d 75% 90%, #722ed1 90% 100%);">
                    <div class="pie-center">收入占比</div>
                </div>
                <div class="pie-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #1890ff;"></div>
                        <div class="legend-text">稻城亚丁香格里拉: 30%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #52c41a;"></div>
                        <div class="legend-text">康定情歌大酒店: 25%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #faad14;"></div>
                        <div class="legend-text">丹巴美人谷度假村: 20%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f5222d;"></div>
                        <div class="legend-text">泸定桥畔精品酒店: 15%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #722ed1;"></div>
                        <div class="legend-text">九龙山温泉酒店: 10%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据总结 -->
        <div class="summary-section">
            <div class="summary-title">📊 数据增强总结</div>
            <p>通过后端数据增强，报表页面现在展示了丰富、真实的业务数据，提供了优秀的演示效果。</p>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <h4>预订趋势</h4>
                    <p>7天数据，79个预订<br>日均11.3个预订</p>
                </div>
                <div class="summary-item">
                    <h4>收入分布</h4>
                    <p>5个酒店，¥94,800总收入<br>百分比数据一致</p>
                </div>
                <div class="summary-item">
                    <h4>关键指标</h4>
                    <p>4个核心指标<br>全部显示增长趋势</p>
                </div>
                <div class="summary-item">
                    <h4>数据质量</h4>
                    <p>100/100分<br>适合生产环境演示</p>
                </div>
            </div>

            <div class="access-links">
                <a href="http://localhost:3004/admin/reports" target="_blank">🔗 访问报表页面</a>
                <a href="http://localhost:3004/admin" target="_blank">🔗 管理后台首页</a>
            </div>
        </div>
    </div>
</body>
</html>
