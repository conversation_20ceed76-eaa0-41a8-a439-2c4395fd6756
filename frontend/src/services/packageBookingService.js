import api from './api';

/**
 * 文化套餐预订服务
 */
class PackageBookingService {
  
  /**
   * 创建预订
   * @param {number} packageId - 套餐ID
   * @param {Object} bookingData - 预订数据
   * @returns {Promise<Object>} 预订结果
   */
  async createBooking(packageId, bookingData) {
    try {
      console.log('创建文化套餐预订:', { packageId, bookingData });
      
      const response = await api.post(`/packages/${packageId}/book`, {
        bookingDate: bookingData.bookingDate,
        participantCount: bookingData.participantCount,
        contactName: bookingData.contactName,
        contactPhone: bookingData.contactPhone,
        contactEmail: bookingData.contactEmail,
        specialRequirements: bookingData.specialRequirements,
        participantInfo: bookingData.participantInfo
      });
      
      console.log('预订创建成功:', response);
      return response;
    } catch (error) {
      console.error('创建预订失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 根据ID获取预订详情
   * @param {number} bookingId - 预订ID
   * @returns {Promise<Object>} 预订详情
   */
  async getBookingById(bookingId) {
    try {
      const response = await api.get(`/packages/bookings/${bookingId}`);
      return response;
    } catch (error) {
      console.error('获取预订详情失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 根据预订号获取预订详情
   * @param {string} bookingNumber - 预订号
   * @returns {Promise<Object>} 预订详情
   */
  async getBookingByNumber(bookingNumber) {
    try {
      const response = await api.get(`/packages/bookings/number/${bookingNumber}`);
      return response;
    } catch (error) {
      console.error('获取预订详情失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 获取当前用户的预订列表
   * @param {number} page - 页码
   * @param {number} size - 每页大小
   * @returns {Promise<Object>} 预订列表
   */
  async getMyBookings(page = 0, size = 10) {
    try {
      const response = await api.get('/packages/bookings/my', {
        params: { page, size }
      });
      return response;
    } catch (error) {
      console.error('获取预订列表失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 确认预订
   * @param {number} bookingId - 预订ID
   * @returns {Promise<Object>} 确认结果
   */
  async confirmBooking(bookingId) {
    try {
      const response = await api.put(`/packages/bookings/${bookingId}/confirm`);
      return response;
    } catch (error) {
      console.error('确认预订失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 取消预订
   * @param {number} bookingId - 预订ID
   * @param {string} reason - 取消原因
   * @returns {Promise<Object>} 取消结果
   */
  async cancelBooking(bookingId, reason) {
    try {
      const response = await api.put(`/packages/bookings/${bookingId}/cancel`, null, {
        params: { reason }
      });
      return response;
    } catch (error) {
      console.error('取消预订失败:', error);
      throw this.handleError(error);
    }
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {Error} 处理后的错误
   */
  handleError(error) {
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response;
      const message = data?.message || data?.error || `请求失败 (${status})`;
      
      switch (status) {
        case 400:
          return new Error(`请求参数错误: ${message}`);
        case 401:
          return new Error('请先登录后再进行预订');
        case 403:
          return new Error('没有权限进行此操作');
        case 404:
          return new Error('请求的资源不存在');
        case 500:
          return new Error('服务器内部错误，请稍后重试');
        default:
          return new Error(message);
      }
    } else if (error.request) {
      // 网络错误
      return new Error('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      return new Error(error.message || '未知错误');
    }
  }

  /**
   * 格式化预订状态
   * @param {string} status - 状态值
   * @returns {Object} 格式化后的状态信息
   */
  formatBookingStatus(status) {
    const statusMap = {
      'PENDING': { text: '待确认', color: 'orange' },
      'CONFIRMED': { text: '已确认', color: 'green' },
      'IN_PROGRESS': { text: '进行中', color: 'blue' },
      'COMPLETED': { text: '已完成', color: 'green' },
      'CANCELLED': { text: '已取消', color: 'red' },
      'NO_SHOW': { text: '未到场', color: 'gray' }
    };
    
    return statusMap[status] || { text: status, color: 'default' };
  }

  /**
   * 格式化支付状态
   * @param {string} paymentStatus - 支付状态值
   * @returns {Object} 格式化后的支付状态信息
   */
  formatPaymentStatus(paymentStatus) {
    const statusMap = {
      'PENDING': { text: '待支付', color: 'orange' },
      'PARTIAL': { text: '部分支付', color: 'blue' },
      'PAID': { text: '已支付', color: 'green' },
      'REFUNDED': { text: '已退款', color: 'purple' },
      'FAILED': { text: '支付失败', color: 'red' }
    };
    
    return statusMap[paymentStatus] || { text: paymentStatus, color: 'default' };
  }
}

// 创建单例实例
const packageBookingService = new PackageBookingService();

export default packageBookingService;
