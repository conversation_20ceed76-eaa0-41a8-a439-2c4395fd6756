import React, { useState, useEffect } from 'react';
import { 
  Card, List, Button, Tag, Modal, message, Spin, Empty, 
  Tabs, Space, Typography, Divider, Popconfirm 
} from 'antd';
import { 
  CalendarOutlined, EnvironmentOutlined, UserOutlined,
  CreditCardOutlined, ExclamationCircleOutlined, EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { bookingService } from '../services/bookingService';
import './MyBookings.css';

// const { TabPane } = Tabs; // 已弃用，使用 items 属性
const { Title, Text } = Typography;

const MyBookings = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 初始化数据
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    loadBookings();
  }, [isAuthenticated, activeTab]);

  // 加载预订列表
  const loadBookings = async () => {
    setLoading(true);
    try {
      const params = {};
      if (activeTab !== 'all') {
        params.status = activeTab.toUpperCase();
      }
      
      const response = await bookingService.getUserBookings(params);
      if (response.success) {
        setBookings(response.data.content || []);
      } else {
        message.error(response.message || '加载预订列表失败');
      }
    } catch (error) {
      console.error('Load bookings failed:', error);
      message.error('加载预订列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消预订
  const handleCancelBooking = async (bookingId) => {
    try {
      const response = await bookingService.cancelBooking(bookingId);
      if (response.success) {
        message.success('预订已取消');
        loadBookings();
      } else {
        message.error(response.message || '取消预订失败');
      }
    } catch (error) {
      console.error('Cancel booking failed:', error);
      message.error('取消预订失败');
    }
  };

  // 查看预订详情
  const handleViewDetail = (booking) => {
    setSelectedBooking(booking);
    setDetailModalVisible(true);
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusConfig = {
      PENDING: { color: 'orange', text: '待确认' },
      CONFIRMED: { color: 'blue', text: '已确认' },
      CHECKED_IN: { color: 'green', text: '已入住' },
      CHECKED_OUT: { color: 'default', text: '已退房' },
      CANCELLED: { color: 'red', text: '已取消' },
      NO_SHOW: { color: 'red', text: '未到店' }
    };

    const config = statusConfig[status] || { color: 'default', text: status || '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取支付状态标签
  const getPaymentStatusTag = (status) => {
    const statusConfig = {
      PENDING: { color: 'orange', text: '待支付' },
      PARTIAL: { color: 'blue', text: '部分支付' },
      PAID: { color: 'green', text: '已支付' },
      REFUNDED: { color: 'default', text: '已退款' },
      FAILED: { color: 'red', text: '支付失败' }
    };

    const config = statusConfig[status] || { color: 'default', text: status || '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染预订项
  const renderBookingItem = (booking) => {
    const canCancel = booking.status === 'PENDING' || booking.status === 'CONFIRMED';
    
    return (
      <List.Item
        key={booking.id}
        actions={[
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(booking)}
          >
            查看详情
          </Button>,
          canCancel && (
            <Popconfirm
              title="确定要取消这个预订吗？"
              onConfirm={() => handleCancelBooking(booking.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                取消预订
              </Button>
            </Popconfirm>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <div className="booking-title">
              <span className="hotel-name">{booking.hotelName}</span>
              <div className="booking-status">
                {getStatusTag(booking.status)}
                {getPaymentStatusTag(booking.paymentStatus)}
              </div>
            </div>
          }
          description={
            <div className="booking-info">
              <div className="booking-detail">
                <CalendarOutlined style={{ marginRight: '8px' }} />
                <span>
                  {booking.checkInDate} 至 {booking.checkOutDate}
                </span>
              </div>
              <div className="booking-detail">
                <EnvironmentOutlined style={{ marginRight: '8px' }} />
                <span>{booking.roomTypeName || booking.roomType}</span>
              </div>
              <div className="booking-detail">
                <UserOutlined style={{ marginRight: '8px' }} />
                <span>{booking.adults || booking.guests || 1}位客人</span>
              </div>
              <div className="booking-detail">
                <CreditCardOutlined style={{ marginRight: '8px' }} />
                <span className="booking-price">¥{booking.finalAmount || booking.totalAmount || 0}</span>
              </div>
            </div>
          }
        />
        <div className="booking-number">
          预订编号：{booking.bookingNumber}
        </div>
      </List.Item>
    );
  };

  // 渲染详情模态框
  const renderDetailModal = () => (
    <Modal
      title="预订详情"
      open={detailModalVisible}
      onCancel={() => setDetailModalVisible(false)}
      footer={[
        <Button key="close" onClick={() => setDetailModalVisible(false)}>
          关闭
        </Button>
      ]}
      width={600}
    >
      {selectedBooking && (
        <div className="booking-detail-content">
          <div className="detail-section">
            <Title level={4}>基本信息</Title>
            <div className="detail-item">
              <span className="detail-label">预订编号：</span>
              <span>{selectedBooking.bookingNumber}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">酒店名称：</span>
              <span>{selectedBooking.hotelName}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">房型：</span>
              <span>{selectedBooking.roomTypeName || selectedBooking.roomType}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">预订状态：</span>
              {getStatusTag(selectedBooking.status)}
            </div>
            <div className="detail-item">
              <span className="detail-label">支付状态：</span>
              {getPaymentStatusTag(selectedBooking.paymentStatus)}
            </div>
          </div>
          
          <Divider />
          
          <div className="detail-section">
            <Title level={4}>入住信息</Title>
            <div className="detail-item">
              <span className="detail-label">入住日期：</span>
              <span>{selectedBooking.checkInDate}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">离店日期：</span>
              <span>{selectedBooking.checkOutDate}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">住宿天数：</span>
              <span>{selectedBooking.totalNights || 1}晚</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">客人数量：</span>
              <span>{selectedBooking.adults || selectedBooking.guests || 1}人</span>
            </div>
          </div>
          
          <Divider />
          
          <div className="detail-section">
            <Title level={4}>费用信息</Title>
            <div className="detail-item">
              <span className="detail-label">房间单价：</span>
              <span>¥{selectedBooking.roomRate || selectedBooking.pricePerNight || 0}/晚</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">总金额：</span>
              <span className="total-price">¥{selectedBooking.finalAmount || selectedBooking.totalAmount || 0}</span>
            </div>
          </div>
          
          {selectedBooking.specialRequests && (
            <>
              <Divider />
              <div className="detail-section">
                <Title level={4}>特殊要求</Title>
                <Text>{selectedBooking.specialRequests}</Text>
              </div>
            </>
          )}
          
          <Divider />
          
          <div className="detail-section">
            <Title level={4}>预订时间</Title>
            <div className="detail-item">
              <span className="detail-label">创建时间：</span>
              <span>{new Date(selectedBooking.createdAt).toLocaleString()}</span>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );

  return (
    <div className="my-bookings-container">
      <Card className="bookings-header-card">
        <Title level={2}>我的预订</Title>
        <Text type="secondary">管理您的所有酒店预订</Text>
      </Card>

      <Card className="bookings-content-card">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="bookings-tabs"
          items={[
            { key: 'all', label: '全部预订' },
            { key: 'pending', label: '待确认' },
            { key: 'confirmed', label: '已确认' },
            { key: 'checked_out', label: '已完成' },
            { key: 'cancelled', label: '已取消' }
          ]}
        />

        <Spin spinning={loading}>
          {bookings.length > 0 ? (
            <List
              className="bookings-list"
              itemLayout="vertical"
              dataSource={bookings}
              renderItem={renderBookingItem}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
            />
          ) : (
            <Empty 
              description="暂无预订记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={() => navigate('/search')}>
                立即预订
              </Button>
            </Empty>
          )}
        </Spin>
      </Card>

      {renderDetailModal()}
    </div>
  );
};

export default MyBookings;
