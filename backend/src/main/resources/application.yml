server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: ganzi-hotel-booking

  datasource:
    url: ************************************************************************************************************
    username: root
    password: hie681130
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true

  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

  # 禁用默认的静态资源处理，避免 /** 模式与控制器冲突
  web:
    resources:
      add-mappings: false

logging:
  level:
    com.ganzi.hotel: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
    org.springframework.web.servlet.mvc.method.annotation: DEBUG

# 应用自定义配置
app:
  upload:
    path: ./uploads
    base-url: http://localhost:8080

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

jwt:
  secret: ganzi-hotel-booking-secret-key-2024
  expiration: 86400000 # 24 hours in milliseconds
