package com.ganzi.hotel.controller;

import com.ganzi.hotel.dto.ApiResponse;
import com.ganzi.hotel.dto.CulturalPackageDto;
import com.ganzi.hotel.dto.PackageBookingDto;
import com.ganzi.hotel.service.SimplifiedCulturalPackageService;
import com.ganzi.hotel.service.PackageBookingService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

/**
 * 简化的文化套餐控制器
 */
@RestController
@RequestMapping("/packages")
public class SimplifiedCulturalPackageController {

    private static final Logger log = LoggerFactory.getLogger(SimplifiedCulturalPackageController.class);
    private final SimplifiedCulturalPackageService packageService;
    private final PackageBookingService packageBookingService;

    public SimplifiedCulturalPackageController(SimplifiedCulturalPackageService packageService,
            PackageBookingService packageBookingService) {
        this.packageService = packageService;
        this.packageBookingService = packageBookingService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<CulturalPackageDto>> getPackageById(@PathVariable Long id) {

        log.info("获取套餐详情，ID: {}", id);

        try {
            Optional<CulturalPackageDto> packageDto = packageService.getPackageById(id);
            if (packageDto.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取套餐详情成功", packageDto.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取套餐详情失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取套餐详情失败: " + e.getMessage()));
        }
    }

    @GetMapping("/recommendations")
    public ResponseEntity<ApiResponse<List<CulturalPackageDto>>> getRecommendedPackages(
            @RequestParam(defaultValue = "6") int limit) {

        log.info("获取推荐套餐，数量: {}", limit);

        try {
            List<CulturalPackageDto> packages = packageService.getRecommendedPackages(limit);
            return ResponseEntity.ok(ApiResponse.success("获取推荐套餐成功", packages));
        } catch (Exception e) {
            log.error("获取推荐套餐失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取推荐套餐失败: " + e.getMessage()));
        }
    }

    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<List<CulturalPackageDto>>> getPopularPackages(
            @RequestParam(defaultValue = "10") int limit) {

        log.info("获取热门套餐，数量: {}", limit);

        try {
            List<CulturalPackageDto> packages = packageService.getPopularPackages(limit);
            return ResponseEntity.ok(ApiResponse.success("获取热门套餐成功", packages));
        } catch (Exception e) {
            log.error("获取热门套餐失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取热门套餐失败: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<CulturalPackageDto>>> getAllActivePackages() {

        log.info("获取所有活跃套餐");

        try {
            List<CulturalPackageDto> packages = packageService.getAllActivePackages();
            return ResponseEntity.ok(ApiResponse.success("获取活跃套餐成功", packages));
        } catch (Exception e) {
            log.error("获取活跃套餐失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取活跃套餐失败: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<CulturalPackageDto>> createPackage(
            @RequestBody @Validated CulturalPackageDto packageDto) {

        log.info("创建新套餐: {}", packageDto.getName());

        try {
            CulturalPackageDto createdPackage = packageService.createPackage(packageDto);
            return ResponseEntity.ok(ApiResponse.success("套餐创建成功", createdPackage));
        } catch (Exception e) {
            log.error("套餐创建失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("套餐创建失败: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/deactivate")
    public ResponseEntity<ApiResponse<String>> deactivatePackage(@PathVariable Long id) {

        log.info("停用套餐: {}", id);

        try {
            packageService.deactivatePackage(id);
            return ResponseEntity.ok(ApiResponse.success("套餐已停用", null));
        } catch (Exception e) {
            log.error("套餐停用失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("套餐停用失败: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}/activate")
    public ResponseEntity<ApiResponse<String>> activatePackage(@PathVariable Long id) {

        log.info("启用套餐: {}", id);

        try {
            packageService.activatePackage(id);
            return ResponseEntity.ok(ApiResponse.success("套餐已启用", null));
        } catch (Exception e) {
            log.error("套餐启用失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("套餐启用失败: " + e.getMessage()));
        }
    }

    /**
     * 更新套餐
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<CulturalPackageDto>> updatePackage(
            @PathVariable Long id,
            @Validated @RequestBody CulturalPackageDto packageDto) {
        log.info("更新套餐: {}", id);

        try {
            Optional<CulturalPackageDto> updatedPackage = packageService.updatePackage(id, packageDto);
            if (updatedPackage.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("套餐更新成功", updatedPackage.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            log.warn("更新套餐失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("更新套餐失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("更新套餐时发生错误", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("更新套餐失败: " + e.getMessage()));
        }
    }

    /**
     * 删除套餐
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deletePackage(@PathVariable Long id) {
        log.info("删除套餐: {}", id);

        try {
            boolean deleted = packageService.deletePackage(id);
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("套餐删除成功", null));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("删除套餐失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("删除套餐失败: " + e.getMessage()));
        }
    }

    /**
     * 创建文化套餐预订
     */
    @PostMapping("/{packageId}/book")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createBooking(
            @PathVariable Long packageId,
            @RequestBody Map<String, Object> bookingData) {

        log.info("创建文化套餐预订，套餐ID: {}", packageId);

        try {
            // 使用PackageBookingService创建预订
            PackageBookingDto booking = packageBookingService.createBooking(packageId, bookingData);

            // 转换为响应格式
            Map<String, Object> bookingResponse = new HashMap<>();
            bookingResponse.put("id", booking.getId());
            bookingResponse.put("bookingNumber", booking.getBookingNumber());
            bookingResponse.put("packageId", booking.getPackageId());
            bookingResponse.put("packageName", booking.getPackageName());
            bookingResponse.put("status", booking.getStatus().name());
            bookingResponse.put("paymentStatus", booking.getPaymentStatus().name());
            bookingResponse.put("totalAmount", booking.getTotalAmount());
            bookingResponse.put("createdAt", booking.getCreatedAt().toString());

            log.info("文化套餐预订创建成功，预订号: {}", booking.getBookingNumber());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("预订创建成功", bookingResponse));

        } catch (IllegalArgumentException e) {
            log.warn("创建预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("创建预订失败: " + e.getMessage()));
        } catch (Exception e) {
            log.error("创建预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("预订失败: " + e.getMessage()));
        }
    }

    /**
     * 生成预订号
     */
    private String generateBookingNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.format("%04d", new Random().nextInt(10000));
        return "PB" + timestamp + random;
    }
}
