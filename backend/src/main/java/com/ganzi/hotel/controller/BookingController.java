package com.ganzi.hotel.controller;

import com.ganzi.hotel.dto.ApiResponse;
import com.ganzi.hotel.dto.BookingDto;
import com.ganzi.hotel.dto.BookingRequestDto;
import com.ganzi.hotel.dto.PaymentDto;
import com.ganzi.hotel.dto.PaymentRequestDto;
import com.ganzi.hotel.entity.User;
import com.ganzi.hotel.service.BookingService;
import com.ganzi.hotel.service.PaymentService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 预订管理控制器
 */
@RestController
@RequestMapping("/bookings")
@CrossOrigin(origins = "http://localhost:3000")
public class BookingController {

    private static final Logger logger = LoggerFactory.getLogger(BookingController.class);

    @Autowired
    private BookingService bookingService;

    @Autowired
    private PaymentService paymentService;

    /**
     * 创建预订
     */
    @PostMapping
    public ResponseEntity<ApiResponse<BookingDto>> createBooking(@Valid @RequestBody BookingRequestDto requestDto) {
        logger.info("创建预订请求: {}", requestDto);

        try {
            // 获取当前用户
            Long userId = getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            BookingDto booking = bookingService.createBooking(userId, requestDto);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("预订创建成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("创建预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("创建预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("创建预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("创建预订失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取预订详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<BookingDto>> getBookingById(@PathVariable Long id) {
        logger.info("获取预订详情，ID: {}", id);

        try {
            Optional<BookingDto> booking = bookingService.getBookingById(id);
            if (booking.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取预订详情成功", booking.get()));
            } else {
                return ResponseEntity.notFound()
                        .build();
            }
        } catch (Exception e) {
            logger.error("获取预订详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订详情失败: " + e.getMessage()));
        }
    }

    /**
     * 根据预订号获取预订详情
     */
    @GetMapping("/number/{bookingNumber}")
    public ResponseEntity<ApiResponse<BookingDto>> getBookingByNumber(@PathVariable String bookingNumber) {
        logger.info("根据预订号获取预订详情: {}", bookingNumber);

        try {
            Optional<BookingDto> booking = bookingService.getBookingByNumber(bookingNumber);
            if (booking.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取预订详情成功", booking.get()));
            } else {
                return ResponseEntity.notFound()
                        .build();
            }
        } catch (Exception e) {
            logger.error("获取预订详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订详情失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户的预订列表
     */
    @GetMapping("/my")
    public ResponseEntity<ApiResponse<Page<BookingDto>>> getMyBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        logger.info("获取当前用户预订列表，页码: {}，大小: {}，状态: {}", page, size, status);

        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<BookingDto> bookings = bookingService.getUserBookings(userId, pageable, status);
            return ResponseEntity.ok(ApiResponse.success("获取预订列表成功", bookings));
        } catch (Exception e) {
            logger.error("获取预订列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取酒店的预订列表（管理员权限）
     */
    @GetMapping("/hotel/{hotelId}")
    // @PreAuthorize("hasRole('ADMIN')") // 暂时注释掉，用于测试
    public ResponseEntity<ApiResponse<Page<BookingDto>>> getHotelBookings(
            @PathVariable Long hotelId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        logger.info("获取酒店预订列表，酒店ID: {}，页码: {}，大小: {}", hotelId, page, size);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<BookingDto> bookings = bookingService.getHotelBookings(hotelId, pageable);
            return ResponseEntity.ok(ApiResponse.success("获取酒店预订列表成功", bookings));
        } catch (Exception e) {
            logger.error("获取酒店预订列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取酒店预订列表失败: " + e.getMessage()));
        }
    }

    /**
     * 确认预订
     */
    @PutMapping("/{id}/confirm")
    public ResponseEntity<ApiResponse<BookingDto>> confirmBooking(@PathVariable Long id) {
        logger.info("确认预订，ID: {}", id);

        try {
            BookingDto booking = bookingService.confirmBooking(id);
            return ResponseEntity.ok(ApiResponse.success("预订确认成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("确认预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("确认预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("确认预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("确认预订失败: " + e.getMessage()));
        }
    }

    /**
     * 取消预订
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<BookingDto>> cancelBooking(
            @PathVariable Long id,
            @RequestParam(required = false) String reason) {
        logger.info("取消预订，ID: {}，原因: {}", id, reason);

        try {
            BookingDto booking = bookingService.cancelBooking(id, reason);
            return ResponseEntity.ok(ApiResponse.success("预订取消成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("取消预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("取消预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("取消预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("取消预订失败: " + e.getMessage()));
        }
    }

    /**
     * 办理入住
     */
    @PutMapping("/{id}/checkin")
    public ResponseEntity<ApiResponse<BookingDto>> checkIn(
            @PathVariable Long id,
            @RequestParam(required = false) Long roomId) {
        logger.info("办理入住，预订ID: {}，房间ID: {}", id, roomId);

        try {
            BookingDto booking = bookingService.checkIn(id, roomId);
            return ResponseEntity.ok(ApiResponse.success("入住办理成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("办理入住失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("办理入住失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("办理入住时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("办理入住失败: " + e.getMessage()));
        }
    }

    /**
     * 办理退房
     */
    @PutMapping("/{id}/checkout")
    public ResponseEntity<ApiResponse<BookingDto>> checkOut(@PathVariable Long id) {
        logger.info("办理退房，预订ID: {}", id);

        try {
            BookingDto booking = bookingService.checkOut(id);
            return ResponseEntity.ok(ApiResponse.success("退房办理成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("办理退房失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("办理退房失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("办理退房时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("办理退房失败: " + e.getMessage()));
        }
    }

    /**
     * 检查房型可用性
     */
    @GetMapping("/availability")
    public ResponseEntity<ApiResponse<Boolean>> checkAvailability(
            @RequestParam Long roomTypeId,
            @RequestParam String checkInDate,
            @RequestParam String checkOutDate) {
        logger.info("检查房型可用性，房型ID: {}，入住日期: {}，退房日期: {}", roomTypeId, checkInDate, checkOutDate);

        try {
            java.time.LocalDate checkIn = java.time.LocalDate.parse(checkInDate);
            java.time.LocalDate checkOut = java.time.LocalDate.parse(checkOutDate);

            boolean available = bookingService.isRoomTypeAvailable(roomTypeId, checkIn, checkOut);
            return ResponseEntity.ok(ApiResponse.success("检查可用性成功", available));
        } catch (Exception e) {
            logger.error("检查房型可用性失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("检查房型可用性失败: " + e.getMessage()));
        }
    }

    /**
     * 处理预订支付
     */
    @PostMapping("/{id}/payment")
    public ResponseEntity<ApiResponse<PaymentDto>> processBookingPayment(
            @PathVariable Long id,
            @RequestBody PaymentRequestDto paymentRequestDto) {
        logger.info("处理预订支付，预订ID: {}，支付请求: {}", id, paymentRequestDto);

        try {
            // 设置预订ID到支付请求中
            paymentRequestDto.setBookingId(id);

            // 验证支付请求数据
            if (paymentRequestDto.getAmount() == null
                    || paymentRequestDto.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("支付金额必须大于0"));
            }

            if (paymentRequestDto.getPaymentMethod() == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("支付方式不能为空"));
            }

            // 获取当前用户
            Long userId = getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponse.error("用户未登录"));
            }

            // 验证预订是否属于当前用户
            Optional<BookingDto> bookingOpt = bookingService.getBookingById(id);
            if (bookingOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            BookingDto booking = bookingOpt.get();
            if (!booking.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("无权限访问此预订"));
            }

            // 创建支付记录
            PaymentDto payment = paymentService.createPayment(paymentRequestDto);

            // 立即处理支付（模拟）
            PaymentDto processedPayment = paymentService.processPayment(payment.getId());

            return ResponseEntity.ok(ApiResponse.success("支付处理成功", processedPayment));
        } catch (IllegalArgumentException e) {
            logger.warn("处理预订支付失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("支付失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("处理预订支付时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("支付失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        logger.debug("Authentication: {}", authentication);
        if (authentication != null) {
            logger.debug("Principal type: {}", authentication.getPrincipal().getClass().getName());
            logger.debug("Principal: {}", authentication.getPrincipal());
            if (authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                logger.debug("User ID: {}", user.getId());
                return user.getId();
            }
        }
        logger.warn("Cannot get current user ID - authentication: {}", authentication);
        return null;
    }
}
