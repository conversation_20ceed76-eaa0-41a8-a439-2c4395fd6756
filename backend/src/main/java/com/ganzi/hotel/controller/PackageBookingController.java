package com.ganzi.hotel.controller;

import com.ganzi.hotel.dto.PackageBookingDto;
import com.ganzi.hotel.entity.PackageBooking;
import com.ganzi.hotel.dto.ApiResponse;
import com.ganzi.hotel.service.PackageBookingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

/**
 * 文化套餐预订管理控制器
 */
@RestController
@RequestMapping("/api/admin/package-bookings")
@CrossOrigin(origins = { "http://localhost:3001", "http://localhost:3003" })
public class PackageBookingController {

    private static final Logger logger = LoggerFactory.getLogger(PackageBookingController.class);

    @Autowired
    private PackageBookingService bookingService;

    /**
     * 获取所有预订列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<PackageBookingDto>>> getAllBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        logger.info("获取所有文化套餐预订列表，页码: {}，大小: {}", page, size);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<PackageBookingDto> bookings = bookingService.getAllBookings(pageable);
            return ResponseEntity.ok(ApiResponse.success("获取预订列表成功", bookings));
        } catch (Exception e) {
            logger.error("获取预订列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订列表失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索预订
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<Page<PackageBookingDto>>> searchBookings(
            @RequestParam(required = false) PackageBooking.BookingStatus status,
            @RequestParam(required = false) PackageBooking.PaymentStatus paymentStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        logger.info("搜索文化套餐预订，状态: {}, 支付状态: {}, 日期范围: {} - {}, 关键词: {}",
                status, paymentStatus, startDate, endDate, keyword);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<PackageBookingDto> bookings = bookingService.searchBookings(
                    status, paymentStatus, startDate, endDate, keyword, pageable);
            return ResponseEntity.ok(ApiResponse.success("搜索预订成功", bookings));
        } catch (Exception e) {
            logger.error("搜索预订失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("搜索预订失败: " + e.getMessage()));
        }
    }

    /**
     * 获取预订详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<PackageBookingDto>> getBookingById(@PathVariable Long id) {
        logger.info("获取文化套餐预订详情，ID: {}", id);

        try {
            Optional<PackageBookingDto> booking = bookingService.getBookingById(id);
            if (booking.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取预订详情成功", booking.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取预订详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订详情失败: " + e.getMessage()));
        }
    }

    /**
     * 根据预订号获取预订详情
     */
    @GetMapping("/number/{bookingNumber}")
    public ResponseEntity<ApiResponse<PackageBookingDto>> getBookingByNumber(@PathVariable String bookingNumber) {
        logger.info("根据预订号获取预订详情: {}", bookingNumber);

        try {
            Optional<PackageBookingDto> booking = bookingService.getBookingByNumber(bookingNumber);
            if (booking.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取预订详情成功", booking.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("根据预订号获取预订详情失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订详情失败: " + e.getMessage()));
        }
    }

    /**
     * 确认预订
     */
    @PutMapping("/{id}/confirm")
    public ResponseEntity<ApiResponse<PackageBookingDto>> confirmBooking(
            @PathVariable Long id,
            @RequestBody(required = false) Map<String, String> requestBody) {
        logger.info("确认文化套餐预订，ID: {}", id);

        try {
            String remarks = requestBody != null ? requestBody.get("remarks") : null;
            PackageBookingDto booking = bookingService.confirmBooking(id, remarks);
            return ResponseEntity.ok(ApiResponse.success("预订确认成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("确认预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("确认预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("确认预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("确认预订失败: " + e.getMessage()));
        }
    }

    /**
     * 拒绝预订
     */
    @PutMapping("/{id}/reject")
    public ResponseEntity<ApiResponse<PackageBookingDto>> rejectBooking(
            @PathVariable Long id,
            @RequestBody Map<String, String> requestBody) {
        logger.info("拒绝文化套餐预订，ID: {}", id);

        try {
            String reason = requestBody.get("reason");
            if (reason == null || reason.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("拒绝预订必须提供原因"));
            }

            PackageBookingDto booking = bookingService.rejectBooking(id, reason);
            return ResponseEntity.ok(ApiResponse.success("预订拒绝成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("拒绝预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("拒绝预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("拒绝预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("拒绝预订失败: " + e.getMessage()));
        }
    }

    /**
     * 取消预订
     */
    @PutMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<PackageBookingDto>> cancelBooking(
            @PathVariable Long id,
            @RequestBody Map<String, String> requestBody) {
        logger.info("取消文化套餐预订，ID: {}", id);

        try {
            String reason = requestBody.get("reason");
            PackageBookingDto booking = bookingService.cancelBooking(id, reason);
            return ResponseEntity.ok(ApiResponse.success("预订取消成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("取消预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("取消预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("取消预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("取消预订失败: " + e.getMessage()));
        }
    }

    /**
     * 完成预订
     */
    @PutMapping("/{id}/complete")
    public ResponseEntity<ApiResponse<PackageBookingDto>> completeBooking(@PathVariable Long id) {
        logger.info("完成文化套餐预订，ID: {}", id);

        try {
            PackageBookingDto booking = bookingService.completeBooking(id);
            return ResponseEntity.ok(ApiResponse.success("预订完成成功", booking));
        } catch (IllegalArgumentException e) {
            logger.warn("完成预订失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("完成预订失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("完成预订时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("完成预订失败: " + e.getMessage()));
        }
    }

    /**
     * 获取预订统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getBookingStatistics() {
        logger.info("获取文化套餐预订统计信息");

        try {
            Map<String, Object> statistics = bookingService.getBookingStatistics();
            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", statistics));
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取统计信息失败: " + e.getMessage()));
        }
    }
}
