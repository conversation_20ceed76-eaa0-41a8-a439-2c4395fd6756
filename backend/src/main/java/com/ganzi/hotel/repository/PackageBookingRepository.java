package com.ganzi.hotel.repository;

import com.ganzi.hotel.entity.PackageBooking;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 文化套餐预订数据访问层
 */
@Repository
public interface PackageBookingRepository extends JpaRepository<PackageBooking, Long> {

    /**
     * 根据预订号查找预订
     */
    Optional<PackageBooking> findByBookingNumber(String bookingNumber);

    /**
     * 根据用户ID查找预订
     */
    @Query("SELECT pb FROM PackageBooking pb " +
           "LEFT JOIN FETCH pb.user " +
           "LEFT JOIN FETCH pb.culturalPackage " +
           "WHERE pb.user.id = :userId " +
           "ORDER BY pb.createdAt DESC")
    Page<PackageBooking> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据套餐ID查找预订
     */
    @Query("SELECT pb FROM PackageBooking pb " +
           "LEFT JOIN FETCH pb.user " +
           "LEFT JOIN FETCH pb.culturalPackage " +
           "WHERE pb.culturalPackage.id = :packageId " +
           "ORDER BY pb.createdAt DESC")
    Page<PackageBooking> findByPackageIdOrderByCreatedAtDesc(@Param("packageId") Long packageId, Pageable pageable);

    /**
     * 根据状态查找预订
     */
    @Query("SELECT pb FROM PackageBooking pb " +
           "LEFT JOIN FETCH pb.user " +
           "LEFT JOIN FETCH pb.culturalPackage " +
           "WHERE pb.status = :status " +
           "ORDER BY pb.createdAt DESC")
    Page<PackageBooking> findByStatusOrderByCreatedAtDesc(@Param("status") PackageBooking.BookingStatus status, Pageable pageable);

    /**
     * 获取所有预订并预加载关联数据
     */
    @Query("SELECT pb FROM PackageBooking pb " +
           "LEFT JOIN FETCH pb.user " +
           "LEFT JOIN FETCH pb.culturalPackage " +
           "ORDER BY pb.createdAt DESC")
    Page<PackageBooking> findAllWithDetails(Pageable pageable);

    /**
     * 根据多个条件搜索预订
     */
    @Query("SELECT pb FROM PackageBooking pb " +
           "LEFT JOIN FETCH pb.user u " +
           "LEFT JOIN FETCH pb.culturalPackage cp " +
           "WHERE (:status IS NULL OR pb.status = :status) " +
           "AND (:paymentStatus IS NULL OR pb.paymentStatus = :paymentStatus) " +
           "AND (:startDate IS NULL OR pb.bookingDate >= :startDate) " +
           "AND (:endDate IS NULL OR pb.bookingDate <= :endDate) " +
           "AND (:keyword IS NULL OR :keyword = '' OR " +
           "     LOWER(pb.bookingNumber) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "     LOWER(pb.contactName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "     LOWER(pb.contactPhone) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "     LOWER(cp.name) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY pb.createdAt DESC")
    Page<PackageBooking> searchBookings(@Param("status") PackageBooking.BookingStatus status,
                                       @Param("paymentStatus") PackageBooking.PaymentStatus paymentStatus,
                                       @Param("startDate") LocalDate startDate,
                                       @Param("endDate") LocalDate endDate,
                                       @Param("keyword") String keyword,
                                       Pageable pageable);

    /**
     * 统计各状态的预订数量
     */
    @Query("SELECT pb.status, COUNT(pb) FROM PackageBooking pb GROUP BY pb.status")
    List<Object[]> countByStatus();

    /**
     * 统计各支付状态的预订数量
     */
    @Query("SELECT pb.paymentStatus, COUNT(pb) FROM PackageBooking pb GROUP BY pb.paymentStatus")
    List<Object[]> countByPaymentStatus();

    /**
     * 获取指定日期范围内的预订统计
     */
    @Query("SELECT DATE(pb.createdAt) as date, COUNT(pb) as count, SUM(pb.totalAmount) as revenue " +
           "FROM PackageBooking pb " +
           "WHERE pb.createdAt >= :startDate AND pb.createdAt <= :endDate " +
           "GROUP BY DATE(pb.createdAt) " +
           "ORDER BY DATE(pb.createdAt)")
    List<Object[]> getBookingStatistics(@Param("startDate") LocalDateTime startDate,
                                       @Param("endDate") LocalDateTime endDate);

    /**
     * 获取热门套餐统计
     */
    @Query("SELECT cp.name, COUNT(pb) as bookingCount, SUM(pb.totalAmount) as totalRevenue " +
           "FROM PackageBooking pb " +
           "JOIN pb.culturalPackage cp " +
           "WHERE pb.createdAt >= :startDate " +
           "GROUP BY cp.id, cp.name " +
           "ORDER BY COUNT(pb) DESC")
    List<Object[]> getPopularPackagesStatistics(@Param("startDate") LocalDateTime startDate, Pageable pageable);

    /**
     * 检查用户是否已预订某套餐
     */
    @Query("SELECT COUNT(pb) > 0 FROM PackageBooking pb " +
           "WHERE pb.user.id = :userId AND pb.culturalPackage.id = :packageId " +
           "AND pb.status NOT IN ('CANCELLED')")
    boolean existsByUserIdAndPackageId(@Param("userId") Long userId, @Param("packageId") Long packageId);

    /**
     * 获取待确认的预订数量
     */
    long countByStatus(PackageBooking.BookingStatus status);

    /**
     * 获取今日新增预订数量
     */
    @Query("SELECT COUNT(pb) FROM PackageBooking pb WHERE DATE(pb.createdAt) = CURRENT_DATE")
    long countTodayBookings();

    /**
     * 获取本月收入
     */
    @Query("SELECT COALESCE(SUM(pb.totalAmount), 0) FROM PackageBooking pb " +
           "WHERE YEAR(pb.createdAt) = YEAR(CURRENT_DATE) " +
           "AND MONTH(pb.createdAt) = MONTH(CURRENT_DATE) " +
           "AND pb.paymentStatus IN ('PAID', 'PARTIAL')")
    java.math.BigDecimal getMonthlyRevenue();
}
