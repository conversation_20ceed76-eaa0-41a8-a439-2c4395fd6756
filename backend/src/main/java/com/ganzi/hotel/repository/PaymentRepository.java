package com.ganzi.hotel.repository;

import com.ganzi.hotel.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 支付数据访问层
 */
@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {

       /**
        * 根据支付号查找支付记录
        */
       Optional<Payment> findByPaymentNumber(String paymentNumber);

       /**
        * 根据预订ID查找支付记录
        */
       List<Payment> findByBookingIdOrderByCreatedAtDesc(Long bookingId);

       /**
        * 根据预订ID和状态查找支付记录
        */
       @Query("SELECT p FROM Payment p WHERE p.booking.id = :bookingId AND p.status = :status ORDER BY p.createdAt DESC")
       List<Payment> findByBookingIdAndStatus(@Param("bookingId") Long bookingId,
                     @Param("status") Payment.PaymentStatus status);

       /**
        * 根据预订ID分页查找支付记录
        */
       Page<Payment> findByBookingIdOrderByCreatedAtDesc(Long bookingId, Pageable pageable);

       /**
        * 根据支付状态查找支付记录
        */
       List<Payment> findByStatusOrderByCreatedAtDesc(Payment.PaymentStatus status);

       /**
        * 根据支付方式查找支付记录
        */
       List<Payment> findByPaymentMethodOrderByCreatedAtDesc(Payment.PaymentMethod paymentMethod);

       /**
        * 根据交易ID查找支付记录
        */
       Optional<Payment> findByTransactionId(String transactionId);

       /**
        * 查找指定时间范围内的支付记录
        */
       @Query("SELECT p FROM Payment p WHERE p.createdAt BETWEEN :startTime AND :endTime ORDER BY p.createdAt DESC")
       List<Payment> findPaymentsInTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 查找指定时间范围内的支付记录（分页）
        */
       @Query("SELECT p FROM Payment p WHERE p.createdAt BETWEEN :startTime AND :endTime ORDER BY p.createdAt DESC")
       Page<Payment> findPaymentsInTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,
                     Pageable pageable);

       /**
        * 查找成功的支付记录
        */
       List<Payment> findByStatusOrderByProcessedAtDesc(Payment.PaymentStatus status);

       /**
        * 查找待处理的支付记录
        */
       @Query("SELECT p FROM Payment p WHERE p.status IN ('PENDING', 'PROCESSING') ORDER BY p.createdAt")
       List<Payment> findPendingPayments();

       /**
        * 查找超时的待支付记录
        */
       @Query("SELECT p FROM Payment p WHERE " +
                     "p.status = 'PENDING' AND " +
                     "p.createdAt < :cutoffTime " +
                     "ORDER BY p.createdAt")
       List<Payment> findExpiredPendingPayments(@Param("cutoffTime") LocalDateTime cutoffTime);

       /**
        * 统计指定预订的支付总金额
        */
       @Query("SELECT COALESCE(SUM(p.amount), 0) FROM Payment p WHERE p.booking.id = :bookingId AND p.status = 'SUCCESS'")
       BigDecimal calculateTotalPaidAmountByBooking(@Param("bookingId") Long bookingId);

       /**
        * 统计指定时间范围内的支付总金额
        */
       @Query("SELECT COALESCE(SUM(p.amount), 0) FROM Payment p WHERE " +
                     "p.status = 'SUCCESS' AND " +
                     "p.processedAt BETWEEN :startTime AND :endTime")
       BigDecimal calculateTotalRevenueInTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 统计各支付方式的使用次数
        */
       @Query("SELECT p.paymentMethod, COUNT(p) FROM Payment p WHERE p.status = 'SUCCESS' GROUP BY p.paymentMethod")
       List<Object[]> countPaymentsByMethod();

       /**
        * 统计各支付状态的数量
        */
       @Query("SELECT p.status, COUNT(p) FROM Payment p GROUP BY p.status")
       List<Object[]> countPaymentsByStatus();

       /**
        * 查找指定用户的支付记录
        */
       @Query("SELECT p FROM Payment p WHERE p.booking.user.id = :userId ORDER BY p.createdAt DESC")
       List<Payment> findByUserId(@Param("userId") Long userId);

       /**
        * 查找指定用户的支付记录（分页）
        */
       @Query("SELECT p FROM Payment p WHERE p.booking.user.id = :userId ORDER BY p.createdAt DESC")
       Page<Payment> findByUserId(@Param("userId") Long userId, Pageable pageable);

       /**
        * 查找指定酒店的支付记录
        */
       @Query("SELECT p FROM Payment p WHERE p.booking.hotel.id = :hotelId ORDER BY p.createdAt DESC")
       List<Payment> findByHotelId(@Param("hotelId") Long hotelId);

       /**
        * 查找指定酒店的支付记录（分页）
        */
       @Query("SELECT p FROM Payment p WHERE p.booking.hotel.id = :hotelId ORDER BY p.createdAt DESC")
       Page<Payment> findByHotelId(@Param("hotelId") Long hotelId, Pageable pageable);

       /**
        * 复合查询支付记录
        */
       @Query("SELECT p FROM Payment p WHERE " +
                     "(:bookingId IS NULL OR p.booking.id = :bookingId) AND " +
                     "(:status IS NULL OR p.status = :status) AND " +
                     "(:paymentMethod IS NULL OR p.paymentMethod = :paymentMethod) AND " +
                     "(:startTime IS NULL OR p.createdAt >= :startTime) AND " +
                     "(:endTime IS NULL OR p.createdAt <= :endTime) " +
                     "ORDER BY p.createdAt DESC")
       Page<Payment> findPaymentsWithFilters(@Param("bookingId") Long bookingId,
                     @Param("status") Payment.PaymentStatus status,
                     @Param("paymentMethod") Payment.PaymentMethod paymentMethod,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,
                     Pageable pageable);

       /**
        * 统计支付成功率
        */
       @Query("SELECT " +
                     "COUNT(CASE WHEN p.status = 'SUCCESS' THEN 1 END) as successCount, " +
                     "COUNT(p) as totalCount " +
                     "FROM Payment p WHERE p.createdAt BETWEEN :startTime AND :endTime")
       Object[] calculatePaymentSuccessRate(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 查找需要退款的支付记录
        */
       @Query("SELECT p FROM Payment p WHERE " +
                     "p.status = 'SUCCESS' AND " +
                     "p.booking.status = 'CANCELLED' AND " +
                     "p.id NOT IN (SELECT r.id FROM Payment r WHERE r.status = 'REFUNDED' AND r.booking.id = p.booking.id) "
                     +
                     "ORDER BY p.processedAt")
       List<Payment> findPaymentsNeedingRefund();

       // ==================== 报表相关查询方法 ====================

       /**
        * 按支付方式统计分布
        */
       @Query("SELECT p.paymentMethod, COALESCE(SUM(p.amount), 0) as totalAmount, COUNT(p) as transactionCount " +
                     "FROM Payment p WHERE p.status = 'SUCCESS' AND " +
                     "DATE(p.processedAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY p.paymentMethod " +
                     "ORDER BY totalAmount DESC")
       List<Object[]> findPaymentMethodDistribution(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按日期统计支付金额
        */
       @Query("SELECT DATE(p.processedAt) as date, COALESCE(SUM(p.amount), 0) as totalAmount, COUNT(p) as transactionCount "
                     +
                     "FROM Payment p WHERE p.status = 'SUCCESS' AND " +
                     "DATE(p.processedAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY DATE(p.processedAt) " +
                     "ORDER BY DATE(p.processedAt)")
       List<Object[]> findDailyPaymentStats(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 计算支付成功率（按日期范围）
        */
       @Query("SELECT " +
                     "(SELECT COUNT(p1) FROM Payment p1 WHERE p1.status = 'SUCCESS' AND DATE(p1.createdAt) BETWEEN :startDate AND :endDate) * 100.0 / "
                     +
                     "(SELECT COUNT(p2) FROM Payment p2 WHERE DATE(p2.createdAt) BETWEEN :startDate AND :endDate) " +
                     "as successRate")
       Double calculatePaymentSuccessRateByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按支付状态统计
        */
       @Query("SELECT p.status, COUNT(p) as count, COALESCE(SUM(p.amount), 0) as totalAmount " +
                     "FROM Payment p WHERE DATE(p.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY p.status")
       List<Object[]> findPaymentStatusDistribution(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按支付方式和日期范围统计分布
        */
       @Query("SELECT p.paymentMethod, COALESCE(SUM(p.amount), 0) as totalAmount, COUNT(p) as transactionCount " +
                     "FROM Payment p WHERE p.status = 'SUCCESS' AND " +
                     "DATE(p.processedAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY p.paymentMethod " +
                     "ORDER BY totalAmount DESC")
       List<Object[]> findPaymentMethodDistributionByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);
}
