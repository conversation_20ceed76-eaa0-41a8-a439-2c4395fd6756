package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.PackageBookingDto;
import com.ganzi.hotel.entity.CulturalPackage;
import com.ganzi.hotel.entity.PackageBooking;
import com.ganzi.hotel.entity.User;
import com.ganzi.hotel.repository.CulturalPackageRepository;
import com.ganzi.hotel.repository.PackageBookingRepository;
import com.ganzi.hotel.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

/**
 * 文化套餐预订服务
 */
@Service
@Transactional
public class PackageBookingService {

    private static final Logger logger = LoggerFactory.getLogger(PackageBookingService.class);

    @Autowired
    private PackageBookingRepository bookingRepository;

    @Autowired
    private CulturalPackageRepository packageRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 创建预订
     */
    public PackageBookingDto createBooking(Long packageId, Map<String, Object> bookingData) {
        logger.info("创建文化套餐预订，套餐ID: {}", packageId);

        // 查找套餐
        CulturalPackage culturalPackage = packageRepository.findById(packageId)
                .orElseThrow(() -> new IllegalArgumentException("套餐不存在"));

        if (!culturalPackage.getIsActive()) {
            throw new IllegalArgumentException("套餐已停用，无法预订");
        }

        // 获取或创建匿名用户
        User anonymousUser = getOrCreateAnonymousUser();

        // 创建预订实体
        PackageBooking booking = new PackageBooking();
        booking.setBookingNumber(generateBookingNumber());
        booking.setCulturalPackage(culturalPackage);
        booking.setUser(anonymousUser);

        // 设置预订数据
        if (bookingData.get("bookingDate") != null) {
            booking.setBookingDate(LocalDate.parse(bookingData.get("bookingDate").toString()));
        }
        if (bookingData.get("participantCount") != null) {
            booking.setParticipantCount(Integer.parseInt(bookingData.get("participantCount").toString()));
        }
        booking.setContactName(bookingData.get("contactName") != null ? bookingData.get("contactName").toString() : "");
        booking.setContactPhone(
                bookingData.get("contactPhone") != null ? bookingData.get("contactPhone").toString() : "");
        booking.setContactEmail(
                bookingData.get("contactEmail") != null ? bookingData.get("contactEmail").toString() : "");
        booking.setSpecialRequirements(
                bookingData.get("specialRequirements") != null ? bookingData.get("specialRequirements").toString()
                        : "");
        booking.setParticipantInfo(
                bookingData.get("participantInfo") != null ? bookingData.get("participantInfo").toString() : "");

        // 计算价格
        booking.setUnitPrice(culturalPackage.getPrice());
        booking.setTotalAmount(culturalPackage.getPrice().multiply(BigDecimal.valueOf(booking.getParticipantCount())));

        // 设置默认状态
        booking.setStatus(PackageBooking.BookingStatus.PENDING);
        booking.setPaymentStatus(PackageBooking.PaymentStatus.PENDING);

        // 保存预订
        PackageBooking savedBooking = bookingRepository.save(booking);
        logger.info("文化套餐预订创建成功，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 获取所有预订
     */
    @Transactional(readOnly = true)
    public Page<PackageBookingDto> getAllBookings(Pageable pageable) {
        logger.info("获取所有文化套餐预订");
        Page<PackageBooking> bookings = bookingRepository.findAllWithDetails(pageable);
        return bookings.map(this::convertToDto);
    }

    /**
     * 根据条件搜索预订
     */
    @Transactional(readOnly = true)
    public Page<PackageBookingDto> searchBookings(PackageBooking.BookingStatus status,
            PackageBooking.PaymentStatus paymentStatus,
            LocalDate startDate,
            LocalDate endDate,
            String keyword,
            Pageable pageable) {
        logger.info("搜索文化套餐预订，状态: {}, 支付状态: {}, 日期范围: {} - {}, 关键词: {}",
                status, paymentStatus, startDate, endDate, keyword);

        Page<PackageBooking> bookings = bookingRepository.searchBookings(
                status, paymentStatus, startDate, endDate, keyword, pageable);
        return bookings.map(this::convertToDto);
    }

    /**
     * 根据ID获取预订详情
     */
    @Transactional(readOnly = true)
    public Optional<PackageBookingDto> getBookingById(Long id) {
        logger.info("获取文化套餐预订详情，ID: {}", id);
        return bookingRepository.findById(id).map(this::convertToDto);
    }

    /**
     * 根据预订号获取预订详情
     */
    @Transactional(readOnly = true)
    public Optional<PackageBookingDto> getBookingByNumber(String bookingNumber) {
        logger.info("根据预订号获取预订详情: {}", bookingNumber);
        return bookingRepository.findByBookingNumber(bookingNumber).map(this::convertToDto);
    }

    /**
     * 确认预订
     */
    public PackageBookingDto confirmBooking(Long id, String remarks) {
        logger.info("确认文化套餐预订，ID: {}, 备注: {}", id, remarks);

        PackageBooking booking = bookingRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != PackageBooking.BookingStatus.PENDING) {
            throw new IllegalArgumentException("只能确认待确认状态的预订");
        }

        booking.setStatus(PackageBooking.BookingStatus.CONFIRMED);
        booking.setConfirmedAt(LocalDateTime.now());
        if (remarks != null && !remarks.trim().isEmpty()) {
            booking.setSpecialRequirements(booking.getSpecialRequirements() + "\n管理员备注: " + remarks);
        }

        PackageBooking savedBooking = bookingRepository.save(booking);
        logger.info("文化套餐预订确认成功，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 拒绝预订
     */
    public PackageBookingDto rejectBooking(Long id, String reason) {
        logger.info("拒绝文化套餐预订，ID: {}, 原因: {}", id, reason);

        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("拒绝预订必须提供原因");
        }

        PackageBooking booking = bookingRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != PackageBooking.BookingStatus.PENDING) {
            throw new IllegalArgumentException("只能拒绝待确认状态的预订");
        }

        booking.setStatus(PackageBooking.BookingStatus.CANCELLED);
        booking.setCancelledAt(LocalDateTime.now());
        booking.setCancellationReason(reason);

        PackageBooking savedBooking = bookingRepository.save(booking);
        logger.info("文化套餐预订拒绝成功，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 取消预订
     */
    public PackageBookingDto cancelBooking(Long id, String reason) {
        logger.info("取消文化套餐预订，ID: {}, 原因: {}", id, reason);

        PackageBooking booking = bookingRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() == PackageBooking.BookingStatus.CANCELLED ||
                booking.getStatus() == PackageBooking.BookingStatus.COMPLETED) {
            throw new IllegalArgumentException("预订已取消或已完成，无法再次取消");
        }

        booking.setStatus(PackageBooking.BookingStatus.CANCELLED);
        booking.setCancelledAt(LocalDateTime.now());
        booking.setCancellationReason(reason);

        PackageBooking savedBooking = bookingRepository.save(booking);
        logger.info("文化套餐预订取消成功，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 完成预订
     */
    public PackageBookingDto completeBooking(Long id) {
        logger.info("完成文化套餐预订，ID: {}", id);

        PackageBooking booking = bookingRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != PackageBooking.BookingStatus.CONFIRMED &&
                booking.getStatus() != PackageBooking.BookingStatus.IN_PROGRESS) {
            throw new IllegalArgumentException("只能完成已确认或进行中的预订");
        }

        booking.setStatus(PackageBooking.BookingStatus.COMPLETED);

        PackageBooking savedBooking = bookingRepository.save(booking);
        logger.info("文化套餐预订完成，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 获取预订统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getBookingStatistics() {
        logger.info("获取文化套餐预订统计信息");

        Map<String, Object> statistics = new HashMap<>();

        // 状态统计
        List<Object[]> statusCounts = bookingRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] row : statusCounts) {
            statusMap.put(row[0].toString(), (Long) row[1]);
        }
        statistics.put("statusCounts", statusMap);

        // 支付状态统计
        List<Object[]> paymentStatusCounts = bookingRepository.countByPaymentStatus();
        Map<String, Long> paymentStatusMap = new HashMap<>();
        for (Object[] row : paymentStatusCounts) {
            paymentStatusMap.put(row[0].toString(), (Long) row[1]);
        }
        statistics.put("paymentStatusCounts", paymentStatusMap);

        // 基础统计
        statistics.put("totalBookings", bookingRepository.count());
        statistics.put("pendingBookings", bookingRepository.countByStatus(PackageBooking.BookingStatus.PENDING));
        statistics.put("todayBookings", bookingRepository.countTodayBookings());
        statistics.put("monthlyRevenue", bookingRepository.getMonthlyRevenue());

        return statistics;
    }

    /**
     * 生成预订号
     */
    private String generateBookingNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = new Random().nextInt(9000) + 1000;
        return "PB" + timestamp + random;
    }

    /**
     * 转换为DTO
     */
    private PackageBookingDto convertToDto(PackageBooking booking) {
        PackageBookingDto dto = new PackageBookingDto();
        dto.setId(booking.getId());
        dto.setBookingNumber(booking.getBookingNumber());

        if (booking.getUser() != null) {
            dto.setUserId(booking.getUser().getId());
            dto.setUserName(booking.getUser().getFullName());
        }

        if (booking.getCulturalPackage() != null) {
            dto.setPackageId(booking.getCulturalPackage().getId());
            dto.setPackageName(booking.getCulturalPackage().getName());
            dto.setPackageLocation(booking.getCulturalPackage().getLocation());
        }

        dto.setBookingDate(booking.getBookingDate());
        dto.setBookingTime(booking.getBookingTime());
        dto.setParticipantCount(booking.getParticipantCount());
        dto.setUnitPrice(booking.getUnitPrice());
        dto.setTotalAmount(booking.getTotalAmount());
        dto.setStatus(booking.getStatus());
        dto.setPaymentStatus(booking.getPaymentStatus());
        dto.setContactName(booking.getContactName());
        dto.setContactPhone(booking.getContactPhone());
        dto.setContactEmail(booking.getContactEmail());
        dto.setSpecialRequirements(booking.getSpecialRequirements());
        dto.setParticipantInfo(booking.getParticipantInfo());
        dto.setConfirmedAt(booking.getConfirmedAt());
        dto.setCancelledAt(booking.getCancelledAt());
        dto.setCancellationReason(booking.getCancellationReason());
        dto.setCreatedAt(booking.getCreatedAt());
        dto.setUpdatedAt(booking.getUpdatedAt());

        return dto;
    }

    /**
     * 获取或创建匿名用户
     */
    private User getOrCreateAnonymousUser() {
        // 查找是否已存在匿名用户
        Optional<User> existingUser = userRepository.findByUsername("anonymous_package_user");

        if (existingUser.isPresent()) {
            return existingUser.get();
        }

        // 创建匿名用户
        User anonymousUser = new User();
        anonymousUser.setUsername("anonymous_package_user");
        anonymousUser.setEmail("<EMAIL>");
        anonymousUser.setFullName("匿名套餐预订用户");
        anonymousUser.setPassword("$2a$10$dummy.password.hash"); // 虚拟密码哈希
        anonymousUser.setRole(User.Role.USER);
        anonymousUser.setIsActive(true);
        anonymousUser.setEmailVerified(false);

        return userRepository.save(anonymousUser);
    }
}
