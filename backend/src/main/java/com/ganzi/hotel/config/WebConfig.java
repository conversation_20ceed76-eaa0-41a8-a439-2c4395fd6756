package com.ganzi.hotel.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.file.Paths;

/**
 * Web配置类 - 配置静态资源访问
 * 重要：限制静态资源处理器的路径模式，避免与控制器端点冲突
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${file.upload.path:uploads}")
    private String uploadPath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 首先清除所有现有的资源处理器
        if (!registry.hasMappingForPattern("/**")) {
            // 只有在没有 /** 映射时才添加我们的配置

            // 配置上传文件的静态资源访问
            String absoluteUploadPath = Paths.get(uploadPath).toAbsolutePath().toString();

            registry.addResourceHandler("/uploads/**")
                    .addResourceLocations("file:" + absoluteUploadPath + "/")
                    .setCachePeriod(3600); // 缓存1小时

            // 配置标准静态资源路径（但不包括 /** 通配符）
            registry.addResourceHandler("/static/**")
                    .addResourceLocations("classpath:/static/");

            registry.addResourceHandler("/public/**")
                    .addResourceLocations("classpath:/public/");

            registry.addResourceHandler("/webjars/**")
                    .addResourceLocations("classpath:/META-INF/resources/webjars/");
        }

        // 注意：不添加 /** 映射，这样控制器端点就不会被静态资源处理器拦截
    }
}
