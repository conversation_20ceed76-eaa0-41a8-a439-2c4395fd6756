package com.ganzi.hotel.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ganzi.hotel.entity.PackageBooking;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 文化套餐预订响应DTO
 */
@Data
public class PackageBookingDto {

    private Long id;
    private String bookingNumber;
    private Long userId;
    private String userName;
    private Long packageId;
    private String packageName;
    private String packageLocation;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate bookingDate;

    @JsonFormat(pattern = "HH:mm")
    private LocalTime bookingTime;

    private Integer participantCount;
    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private PackageBooking.BookingStatus status;
    private PackageBooking.PaymentStatus paymentStatus;
    private String contactName;
    private String contactPhone;
    private String contactEmail;
    private String specialRequirements;
    private String participantInfo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelledAt;

    private String cancellationReason;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 便利方法
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    public String getPaymentStatusDescription() {
        return paymentStatus != null ? paymentStatus.getDescription() : "";
    }
}
