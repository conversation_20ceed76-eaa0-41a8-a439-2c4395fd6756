# 管理员后台 API 接口规范

## 📋 概述

本文档定义了甘孜州酒店预订系统管理员后台所需的 API 接口规范，包括现有接口和需要新增的接口。

## 🔐 认证方式

所有管理员 API 都需要在请求头中携带 JWT Token：

```http
Authorization: Bearer <jwt_token>
```

管理员权限验证：

- 用户角色必须为 `ADMIN`
- Token 必须有效且未过期

## 📊 仪表板 API

### 获取系统统计概览

```http
GET /api/admin/dashboard/stats
```

**响应示例**:

```json
{
  "success": true,
  "message": "获取统计数据成功",
  "data": {
    "users": {
      "total": 1250,
      "todayNew": 15,
      "activeUsers": 1180,
      "adminCount": 5
    },
    "hotels": {
      "total": 45,
      "active": 42,
      "avgRating": 4.2,
      "todayNew": 1
    },
    "bookings": {
      "total": 3580,
      "todayNew": 28,
      "totalRevenue": 2580000.0,
      "todayRevenue": 15600.0,
      "statusDistribution": [
        { "name": "待确认", "value": 120 },
        { "name": "已确认", "value": 850 },
        { "name": "已入住", "value": 1200 },
        { "name": "已退房", "value": 1100 },
        { "name": "已取消", "value": 280 },
        { "name": "未到店", "value": 30 }
      ]
    },
    "reviews": {
      "total": 2150,
      "pending": 12,
      "avgRating": 4.3
    }
  }
}
```

### 获取趋势数据

```http
GET /api/admin/dashboard/trends?period=7d&type=bookings
```

**参数**:

- `period`: 时间周期 (7d, 30d, 90d, 1y)
- `type`: 数据类型 (bookings, revenue, users, reviews)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "labels": [
      "2025-07-14",
      "2025-07-15",
      "2025-07-16",
      "2025-07-17",
      "2025-07-18",
      "2025-07-19",
      "2025-07-20"
    ],
    "datasets": [
      {
        "label": "预订数量",
        "data": [25, 32, 28, 35, 42, 38, 28]
      }
    ]
  }
}
```

## 👥 用户管理 API

### 获取用户列表

```http
GET /api/admin/users?page=0&size=20&search=&role=&status=
```

**参数**:

- `page`: 页码 (从 0 开始)
- `size`: 每页大小
- `search`: 搜索关键词 (用户名、邮箱、姓名)
- `role`: 角色筛选 (USER, ADMIN)
- `status`: 状态筛选 (active, inactive)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "fullName": "测试用户",
        "phone": "13900139000",
        "role": "USER",
        "isActive": true,
        "emailVerified": true,
        "createdAt": "2025-07-20T10:30:00",
        "lastLoginAt": "2025-07-20T15:45:00"
      }
    ],
    "totalElements": 1250,
    "totalPages": 63,
    "size": 20,
    "number": 0
  }
}
```

### 获取用户详情

```http
GET /api/admin/users/{id}
```

### 更新用户信息

```http
PUT /api/admin/users/{id}
```

**请求体**:

```json
{
  "fullName": "更新的姓名",
  "phone": "13800138000",
  "role": "USER",
  "isActive": true,
  "emailVerified": true
}
```

### 禁用/启用用户

```http
POST /api/admin/users/{id}/toggle-status
```

### 重置用户密码

```http
POST /api/admin/users/{id}/reset-password
```

**请求体**:

```json
{
  "newPassword": "newPassword123"
}
```

## 🏨 酒店管理 API (扩展)

### 获取酒店管理列表

```http
GET /api/admin/hotels?page=0&size=20&city=&status=&starRating=
```

**响应包含额外的管理信息**:

```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "name": "康定情歌大酒店",
        "city": "康定",
        "starRating": 4,
        "isActive": true,
        "totalRooms": 120,
        "availableRooms": 85,
        "occupancyRate": 0.71,
        "totalBookings": 450,
        "avgRating": 4.2,
        "createdAt": "2025-01-15T10:00:00",
        "lastUpdated": "2025-07-20T14:30:00"
      }
    ]
  }
}
```

### 批量操作酒店

```http
POST /api/admin/hotels/batch
```

**请求体**:

```json
{
  "action": "activate|deactivate|delete",
  "hotelIds": [1, 2, 3, 4]
}
```

## 🛏️ 房间管理 API (扩展)

### 获取房间管理列表

```http
GET /api/admin/rooms?page=0&size=20&hotelId=&status=&roomType=
```

### 批量更新房间状态

```http
POST /api/admin/rooms/batch-status
```

**请求体**:

```json
{
  "roomIds": [1, 2, 3],
  "status": "AVAILABLE|OCCUPIED|MAINTENANCE|OUT_OF_ORDER"
}
```

## 📅 预订管理 API (扩展)

### 获取预订管理列表

```http
GET /api/admin/bookings?page=0&size=20&status=&hotelId=&dateFrom=&dateTo=
```

**响应包含额外管理信息**:

```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "bookingNumber": "BK20250720001",
        "user": {
          "id": 1,
          "username": "testuser",
          "fullName": "测试用户"
        },
        "hotel": {
          "id": 1,
          "name": "康定情歌大酒店"
        },
        "room": {
          "id": 1,
          "roomNumber": "201",
          "roomType": "标准间"
        },
        "checkInDate": "2025-07-25",
        "checkOutDate": "2025-07-27",
        "totalAmount": 576.0,
        "status": "CONFIRMED",
        "paymentStatus": "PAID",
        "createdAt": "2025-07-20T16:30:00",
        "canCancel": true,
        "canModify": true
      }
    ]
  }
}
```

### 管理员操作预订

```http
POST /api/admin/bookings/{id}/action
```

**请求体**:

```json
{
  "action": "confirm|cancel|checkin|checkout|modify",
  "reason": "操作原因",
  "refundAmount": 500.0
}
```

## ⭐ 评价管理 API (现有接口)

### 获取待审核评价

```http
GET /api/reviews/pending?page=0&size=20
```

### 审核评价

```http
PUT /api/reviews/{id}/approve
```

### 管理员回复评价

```http
PUT /api/reviews/{id}/reply
```

### 隐藏评价

```http
PUT /api/reviews/{id}/hide
```

## 🎭 文化套餐管理 API (现有接口)

### 获取套餐统计

```http
GET /api/cultural-packages/statistics
```

### 创建文化套餐

```http
POST /api/cultural-packages
```

### 更新文化套餐

```http
PUT /api/cultural-packages/{id}
```

## 🔔 通知管理 API

### 获取系统通知列表

```http
GET /api/admin/notifications?page=0&size=20&type=&status=
```

### 发送系统公告

```http
POST /api/notifications/announcement
```

### 批量删除通知

```http
DELETE /api/admin/notifications/batch
```

**请求体**:

```json
{
  "notificationIds": [1, 2, 3, 4]
}
```

## ⚙️ 系统设置 API

### 获取系统设置

```http
GET /api/admin/settings
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "siteName": "甘孜州酒店预订系统",
    "siteDescription": "专业的甘孜州酒店预订平台",
    "defaultCurrency": "CNY",
    "bookingAdvanceDays": 365,
    "cancellationHours": 24,
    "taxRate": 0.06,
    "adminEmail": "<EMAIL>",
    "maintenanceMode": false,
    "registrationEnabled": true,
    "emailNotificationEnabled": true,
    "smsNotificationEnabled": false
  }
}
```

### 更新系统设置

```http
PUT /api/admin/settings
```

### 系统维护模式

```http
POST /api/admin/maintenance
```

**请求体**:

```json
{
  "enabled": true,
  "message": "系统维护中，预计2小时后恢复正常",
  "estimatedTime": "2025-07-20T20:00:00"
}
```

## 📊 报表统计 API

### 获取收入报表

```http
GET /api/admin/reports/revenue?period=monthly&year=2025&month=7
```

### 获取预订报表

```http
GET /api/admin/reports/bookings?period=daily&dateFrom=2025-07-01&dateTo=2025-07-31
```

### 获取用户活跃度报表

```http
GET /api/admin/reports/user-activity?period=weekly&weeks=4
```

### 导出报表

```http
GET /api/admin/reports/export?type=revenue&format=excel&period=monthly&year=2025
```

## 🔍 搜索和筛选 API

### 全局搜索

```http
GET /api/admin/search?q=关键词&type=users,hotels,bookings&limit=10
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser",
        "fullName": "测试用户",
        "type": "user"
      }
    ],
    "hotels": [
      {
        "id": 1,
        "name": "康定情歌大酒店",
        "city": "康定",
        "type": "hotel"
      }
    ],
    "bookings": [
      {
        "id": 1,
        "bookingNumber": "BK20250720001",
        "hotelName": "康定情歌大酒店",
        "type": "booking"
      }
    ]
  }
}
```

## 📝 操作日志 API

### 获取操作日志

```http
GET /api/admin/audit-logs?page=0&size=20&userId=&action=&dateFrom=&dateTo=
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1,
        "userId": 1,
        "username": "admin",
        "action": "UPDATE_HOTEL",
        "resourceType": "HOTEL",
        "resourceId": 1,
        "description": "更新酒店信息：康定情歌大酒店",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "createdAt": "2025-07-20T16:45:00"
      }
    ]
  }
}
```

## 🚨 错误处理

### 标准错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2025-07-20T16:30:00"
}
```

### 常见错误码

- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `VALIDATION_ERROR`: 参数验证失败
- `BUSINESS_ERROR`: 业务逻辑错误
- `SYSTEM_ERROR`: 系统内部错误

---

**文档版本**: v1.0  
**创建日期**: 2025-07-20  
**最后更新**: 2025-07-20
