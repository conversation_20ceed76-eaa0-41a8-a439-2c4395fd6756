<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预订显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .booking-item {
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .price {
            font-weight: bold;
            color: #e74c3c;
        }
        .status {
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
        }
        .status.pending { background: #f39c12; }
        .status.confirmed { background: #3498db; }
        .status.paid { background: #27ae60; }
    </style>
</head>
<body>
    <h1>预订显示修复测试</h1>
    
    <div class="test-section">
        <h2>测试数据</h2>
        <p>基于API返回的实际数据结构进行测试</p>
        
        <div class="booking-item">
            <h3>预订编号: BK17570638758205F347C6C</h3>
            <p><strong>酒店:</strong> SCXY19881593393</p>
            <p><strong>房型:</strong> 标准间</p>
            <p><strong>入住日期:</strong> 2025-09-10 至 2025-09-12</p>
            <p><strong>客人数量:</strong> 2人</p>
            <p><strong>房间单价:</strong> ¥200.00/晚</p>
            <p><strong>总金额:</strong> <span class="price">¥400.00</span></p>
            <p><strong>最终金额:</strong> <span class="price">¥440.00</span> (含税费)</p>
            <p><strong>预订状态:</strong> <span class="status pending">待确认</span></p>
            <p><strong>支付状态:</strong> <span class="status pending">待支付</span></p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>修复内容</h2>
        <ul>
            <li>✅ 金额显示字段从 <code>totalPrice</code> 修复为 <code>finalAmount || totalAmount</code></li>
            <li>✅ 房间单价字段从 <code>pricePerNight</code> 修复为 <code>roomRate</code></li>
            <li>✅ 房型显示字段从 <code>roomType</code> 修复为 <code>roomTypeName || roomType</code></li>
            <li>✅ 客人数量字段从 <code>guests</code> 修复为 <code>adults || guests</code></li>
            <li>✅ 添加了缺失的预订状态 <code>NO_SHOW</code></li>
            <li>✅ 添加了缺失的支付状态 <code>PARTIAL</code></li>
            <li>✅ 改进了状态映射的容错处理</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>API数据结构</h2>
        <pre style="background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto;">
{
  "id": 6,
  "bookingNumber": "BK17570638758205F347C6C",
  "checkInDate": "2025-09-10",
  "checkOutDate": "2025-09-12",
  "adults": 2,
  "children": 0,
  "totalNights": 2,
  "roomRate": 200.00,
  "totalAmount": 400.00,
  "taxAmount": 40.00,
  "discountAmount": 0.00,
  "finalAmount": 440.00,
  "status": "PENDING",
  "paymentStatus": "PENDING",
  "hotelName": "SCXY19881593393",
  "roomTypeName": "标准间"
}
        </pre>
    </div>
    
    <div class="test-section">
        <h2>验证步骤</h2>
        <ol>
            <li>访问用户前端: <a href="http://localhost:3002/profile/bookings" target="_blank">http://localhost:3002/profile/bookings</a></li>
            <li>使用测试账户登录: testfix / Test123456</li>
            <li>查看预订列表，确认金额和状态显示正确</li>
            <li>点击查看详情，确认详情页面显示正确</li>
            <li>访问管理后台: <a href="http://localhost:3001/admin/bookings" target="_blank">http://localhost:3001/admin/bookings</a></li>
            <li>使用管理员账户登录: admin / admin123</li>
            <li>查看预订管理页面，确认显示正确</li>
        </ol>
    </div>
</body>
</html>
