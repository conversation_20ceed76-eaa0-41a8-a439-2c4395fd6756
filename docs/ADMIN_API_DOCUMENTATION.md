# 管理后台 API 接口文档

## 📋 概述

本文档记录了甘孜州酒店管理系统管理后台的所有 API 接口。管理后台系统提供了完整的企业级酒店管理功能，包括财务管理、报表分析、系统监控等高级功能。

## 🔐 认证说明

所有管理后台 API 都需要 JWT 令牌认证，请在请求头中包含：

```
Authorization: Bearer <your_jwt_token>
```

## 📊 仪表板 API

### 获取仪表板统计数据

```http
GET /api/admin/dashboard/stats
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "totalHotels": 25,
    "totalRooms": 500,
    "totalBookings": 1250,
    "totalUsers": 3200,
    "totalReviews": 850,
    "monthlyRevenue": 1250000,
    "occupancyRate": 78.5,
    "averageRating": 4.3
  }
}
```

### 获取趋势数据

```http
GET /api/admin/dashboard/trends?period=30d
```

## 💰 财务管理 API

### 获取财务概览

```http
GET /api/admin/finance/overview
```

**查询参数**:

- `startDate`: 开始日期 (YYYY-MM-DD)
- `endDate`: 结束日期 (YYYY-MM-DD)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "totalRevenue": 1250000,
    "totalExpense": 850000,
    "netProfit": 400000,
    "profitMargin": 32.0,
    "monthlyGrowth": 15.8,
    "bookingRevenue": 1000000,
    "serviceRevenue": 200000,
    "otherRevenue": 50000
  }
}
```

### 获取收入数据

```http
GET /api/admin/finance/revenue
```

### 获取支出数据

```http
GET /api/admin/finance/expenses
```

### 创建支出记录

```http
POST /api/admin/finance/expenses
```

**请求体**:

```json
{
  "category": "人员工资",
  "amount": 150000,
  "description": "2024年12月员工工资",
  "date": "2024-12-01"
}
```

### 审批支出

```http
PUT /api/admin/finance/expenses/:id/approve
```

## 🛏️ 房间调度 API

### 获取房间调度数据

```http
GET /api/admin/rooms/schedule
```

**查询参数**:

- `date`: 日期 (YYYY-MM-DD)
- `floor`: 楼层
- `status`: 房间状态

### 获取维护任务

```http
GET /api/admin/rooms/maintenance
```

### 创建维护任务

```http
POST /api/admin/rooms/maintenance
```

**请求体**:

```json
{
  "roomId": 101,
  "type": "ROUTINE",
  "priority": "MEDIUM",
  "description": "空调系统例行检查",
  "scheduledDate": "2024-12-25",
  "estimatedDuration": 2,
  "assignedTo": "维修师傅A"
}
```

````

### 获取团体预订

```http
GET /api/admin/bookings/groups
````

````

## 📊 报表分析 API

> **🎯 重要更新**: 报表系统已从模拟数据切换到真实数据库数据，所有统计信息都基于实际业务数据生成。

### 获取收入报表

```http
GET /api/admin/reports/revenue
````

**查询参数**:

- `period`: 时间周期 (DAILY|WEEKLY|MONTHLY|QUARTERLY|YEARLY)
- `startDate`: 开始日期 (YYYY-MM-DD)
- `endDate`: 结束日期 (YYYY-MM-DD)
- `hotelIds`: 酒店 ID 列表 (可选)
- `includeDetails`: 是否包含详细数据 (默认: false)

**响应示例**:

```json
{
  "success": true,
  "data": {
    "title": "收入报表",
    "reportType": "REVENUE",
    "timePeriod": "DAILY",
    "startDate": "2025-06-22",
    "endDate": "2025-07-22",
    "generatedAt": "2025-07-22T05:37:29.931Z",
    "summary": {
      "totalRevenue": 450000,
      "avgDailyRevenue": 15000,
      "yearOverYearGrowth": 12.5,
      "monthOverMonthGrowth": 3.2
    },
    "data": {
      "timeSeries": [...],
      "categories": [...],
      "hotelRanking": [...]
    }
  }
}
```

### 获取预订报表

```http
GET /api/admin/reports/bookings
```

**查询参数**: 同收入报表

**响应示例**:

```json
{
  "success": true,
  "data": {
    "title": "预订报表",
    "reportType": "BOOKING",
    "summary": {
      "totalBookings": 1250,
      "newBookings": 450,
      "cancelledBookings": 85,
      "completedBookings": 715,
      "cancellationRate": 6.8,
      "completionRate": 57.2
    },
    "data": {
      "trends": [...],
      "statusDistribution": [...],
      "sources": [...]
    }
  }
}
```

### 获取酒店表现报表

```http
GET /api/admin/reports/hotel-performance
```

**查询参数**: 同收入报表

**响应示例**:

```json
{
  "success": true,
  "data": {
    "title": "酒店表现报表",
    "reportType": "HOTEL_PERFORMANCE",
    "summary": {
      "totalHotels": 13,
      "avgOccupancyRate": 78.5,
      "avgRating": 4.5,
      "totalRevenue": 650000,
      "totalBookings": 1800
    },
    "data": {
      "hotelRanking": [
        {
          "hotelName": "康定情歌大酒店",
          "bookingCount": 200,
          "revenue": 150000,
          "rating": 4.8,
          "occupancyRate": 85
        }
      ]
    }
  }
}
```

### 获取用户分析报表

```http
GET /api/admin/reports/user-analytics
```

**查询参数**: 同收入报表（不包含 hotelIds）

**响应示例**:

```json
{
  "success": true,
  "data": {
    "title": "用户分析报表",
    "reportType": "USER_ANALYTICS",
    "summary": {
      "totalUsers": 5341,
      "newUsers": 245,
      "activeUsers": 1286,
      "retentionRate": 40.0,
      "avgUserValue": 1200,
      "lifetimeValue": 3600
    },
    "data": {
      "growthTrends": [...],
      "activityAnalysis": [...],
      "locationDistribution": [...]
    }
  }
}
```

### 导出报表

```http
GET /api/admin/reports/export
```

**查询参数**:

- `reportType`: 报表类型 (REVENUE|BOOKING|USER_ANALYTICS|HOTEL_PERFORMANCE)
- `format`: 导出格式 (CSV|EXCEL|PDF)
- `period`: 时间周期 (DAILY|WEEKLY|MONTHLY|QUARTERLY|YEARLY)
- `startDate`: 开始日期 (YYYY-MM-DD)
- `endDate`: 结束日期 (YYYY-MM-DD)
- `includeDetails`: 是否包含详细数据 (默认: true)

**响应**: 直接下载文件

### 🔍 数据来源说明

**真实数据源**:

- **收入数据**: 来自 `bookings` 表的 `final_amount` 字段，仅统计已支付订单
- **预订数据**: 来自 `bookings` 表的完整预订记录
- **用户数据**: 来自 `users` 表的用户注册和活跃数据
- **酒店数据**: 来自 `hotels` 表结合预订和评价数据

**数据处理逻辑**:

- 所有金额计算基于实际支付状态 (`paymentStatus = 'PAID'`)
- 时间范围过滤基于记录创建时间 (`created_at`)
- 评分数据来自 `reviews` 表的真实用户评价
- 入住率基于房间预订与总房间数的比例

## 🖥️ 系统监控 API

### 获取系统状态

```http
GET /api/admin/system/status
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "cpu": {
      "usage": 45,
      "cores": 8,
      "temperature": 52
    },
    "memory": {
      "total": 16384,
      "used": 8192,
      "usage": 50
    },
    "disk": {
      "total": 1024000,
      "used": 512000,
      "usage": 50
    },
    "uptime": 604800
  }
}
```

### 获取服务状态

```http
GET /api/admin/system/services
```

### 获取系统日志

```http
GET /api/admin/system/logs
```

**查询参数**:

- `level`: 日志级别 (INFO|WARN|ERROR|DEBUG)
- `service`: 服务名称
- `page`: 页码
- `size`: 每页大小

### 获取告警信息

```http
GET /api/admin/system/alerts
```

## 📢 通知管理 API

### 获取站内消息

```http
GET /api/admin/notifications/messages
```

### 发送站内消息

```http
POST /api/admin/notifications/messages
```

**请求体**:

```json
{
  "type": "SYSTEM",
  "priority": "HIGH",
  "title": "系统维护通知",
  "content": "系统将于今晚23:00-01:00进行维护升级",
  "recipients": ["all"],
  "sendTime": "now"
}
```

### 获取通知模板

```http
GET /api/admin/notifications/templates
```

### 创建通知模板

```http
POST /api/admin/notifications/templates
```

**请求体**:

```json
{
  "name": "预订确认邮件",
  "type": "BOOKING",
  "channel": "EMAIL",
  "subject": "预订确认 - {{hotelName}}",
  "content": "尊敬的{{guestName}}，您的预订已确认...",
  "isActive": true
}
```

### 获取发送记录

```http
GET /api/admin/notifications/records
```

## 🔧 系统设置 API

### 获取系统设置

```http
GET /api/admin/settings
```

### 更新系统设置

```http
PUT /api/admin/settings
```

**请求体**:

```json
{
  "siteName": "甘孜州酒店管理系统",
  "siteDescription": "专业的酒店管理平台",
  "contactEmail": "<EMAIL>",
  "maintenanceMode": false,
  "allowRegistration": true
}
```

## 📈 错误码说明

| 错误码 | 说明           |
| ------ | -------------- |
| 200    | 请求成功       |
| 400    | 请求参数错误   |
| 401    | 未授权访问     |
| 403    | 权限不足       |
| 404    | 资源不存在     |
| 409    | 资源冲突       |
| 422    | 数据验证失败   |
| 500    | 服务器内部错误 |

## 🔄 分页响应格式

```json
{
  "success": true,
  "data": {
    "content": [], // 数据列表
    "totalElements": 100, // 总记录数
    "totalPages": 10, // 总页数
    "size": 10, // 每页大小
    "number": 0, // 当前页码
    "first": true, // 是否第一页
    "last": false // 是否最后一页
  }
}
```

## 📝 请求示例

### 使用 curl 请求示例

```bash
# 登录获取token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 使用token访问管理接口
curl -X GET http://localhost:3000/api/admin/dashboard/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 创建支出记录
curl -X POST http://localhost:3000/api/admin/finance/expenses \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "办公用品",
    "amount": 5000,
    "description": "办公用品采购",
    "date": "2024-12-20"
  }'
```

### 使用 JavaScript 请求示例

```javascript
// 设置默认请求头
const token = localStorage.getItem("admin_token");
const headers = {
  Authorization: `Bearer ${token}`,
  "Content-Type": "application/json",
};

// 获取仪表板数据
const dashboardStats = await fetch("/api/admin/dashboard/stats", {
  headers,
}).then((res) => res.json());

// 发送站内消息
const sendMessage = await fetch("/api/admin/notifications/messages", {
  method: "POST",
  headers,
  body: JSON.stringify({
    type: "SYSTEM",
    priority: "HIGH",
    title: "系统通知",
    content: "这是一条测试消息",
    recipients: ["all"],
  }),
}).then((res) => res.json());
```

---

**文档版本**: v2.0.0  
**更新时间**: 2024-12-20  
**维护者**: 开发团队
