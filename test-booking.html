<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文化套餐预订</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #1890ff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f6f6f6;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <h1>文化套餐预订测试</h1>
    <p><strong>测试状态：</strong>✅ 后端API正常工作，前端登录检查已修复</p>
    <p><strong>最新测试：</strong>预订号 PB202509061603377881 创建成功</p>
    
    <form id="bookingForm">
        <div class="form-group">
            <label for="packageId">套餐ID:</label>
            <select id="packageId" required>
                <option value="8">藏族传统文化体验之旅 (ID: 8)</option>
                <option value="9">高原徒步探险 (ID: 9)</option>
                <option value="10">藏式美食烹饪课程 (ID: 10)</option>
                <option value="11">测试套餐 (ID: 11)</option>
                <option value="12">测试更新套餐 (ID: 12)</option>
                <option value="15">苍穹外卖 (ID: 15)</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="bookingDate">预订日期:</label>
            <input type="date" id="bookingDate" required>
        </div>
        
        <div class="form-group">
            <label for="participantCount">参与人数:</label>
            <input type="number" id="participantCount" min="1" max="20" value="2" required>
        </div>
        
        <div class="form-group">
            <label for="contactName">联系人姓名:</label>
            <input type="text" id="contactName" value="测试用户" required>
        </div>
        
        <div class="form-group">
            <label for="contactPhone">联系电话:</label>
            <input type="tel" id="contactPhone" value="13800138000" required>
        </div>
        
        <div class="form-group">
            <label for="contactEmail">联系邮箱:</label>
            <input type="email" id="contactEmail" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="specialRequirements">特殊要求:</label>
            <textarea id="specialRequirements" rows="3" placeholder="请输入特殊要求（可选）"></textarea>
        </div>
        
        <div class="form-group">
            <label for="participantInfo">参与者信息:</label>
            <textarea id="participantInfo" rows="3" placeholder="请输入参与者详细信息（可选）"></textarea>
        </div>
        
        <button type="submit">提交预订</button>
    </form>
    
    <div id="result"></div>

    <script>
        // 设置默认日期为明天
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('bookingDate').value = tomorrow.toISOString().split('T')[0];

        document.getElementById('bookingForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const bookingData = {
                bookingDate: document.getElementById('bookingDate').value,
                participantCount: parseInt(document.getElementById('participantCount').value),
                contactName: document.getElementById('contactName').value,
                contactPhone: document.getElementById('contactPhone').value,
                contactEmail: document.getElementById('contactEmail').value,
                specialRequirements: document.getElementById('specialRequirements').value,
                participantInfo: document.getElementById('participantInfo').value
            };
            
            const packageId = document.getElementById('packageId').value;
            
            try {
                const response = await fetch(`http://localhost:8080/api/packages/${packageId}/book`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(bookingData)
                });
                
                const result = await response.json();
                
                const resultDiv = document.getElementById('result');
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>预订成功！</h3>
                        <p><strong>预订号：</strong>${result.data.bookingNumber}</p>
                        <p><strong>套餐名称：</strong>${result.data.packageName}</p>
                        <p><strong>预订日期：</strong>${result.data.bookingDate}</p>
                        <p><strong>参与人数：</strong>${result.data.participantCount}人</p>
                        <p><strong>联系人：</strong>${result.data.contactName}</p>
                        <p><strong>联系电话：</strong>${result.data.contactPhone}</p>
                        <p><strong>预订状态：</strong>${result.data.status === 'PENDING' ? '待确认' : result.data.status}</p>
                        <p><strong>支付状态：</strong>${result.data.paymentStatus === 'PENDING' ? '待支付' : result.data.paymentStatus}</p>
                        <p style="margin-top: 15px; color: #666;">
                            请保存好您的预订号，我们将尽快与您联系确认预订详情。
                        </p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>预订失败</h3>
                        <p>${result.message}</p>
                    `;
                }
            } catch (error) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>预订失败</h3>
                    <p>网络错误：${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
